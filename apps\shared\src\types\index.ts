// Enums
export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  VIDEO = 'video',
  EMOJI = 'emoji',
  PDF = 'pdf',
  GIF = 'gif',
  LIKE = 'like',
  DISLIKE = 'dislike',
  REPLY = 'reply'
}

export enum Language {
  EN = 'en',
  TR = 'tr',
  ES = 'es',
  FR = 'fr',
  DE = 'de'
}

// User related types
export interface User {
  id: number;
  email: string;
  phone: string;
  username: string;
  password: string; // hashed
  bio?: string;
  profile_photo?: string;
  country?: string;
  city?: string;
  user_preferences: Record<string, any>; // JSO<PERSON>
  created_at: Date;
  is_banned: boolean;
  banned_until?: Date;
  is_admin: boolean;
  is_verified: boolean;
  otp_code?: number;
  otp_expiretion_date?: Date;
}

// Group related types
export interface Group {
  id: number;
  region?: string;
  city?: string;
  country: string; // not nullable for central groups
  birth_year?: number;
  language: Language;
  topic?: string; // e.g. ek gida
  created_at: Date;
  is_public: boolean;
}

export interface GroupMember {
  id: number;
  user_id: number; // FK → USERS.id
  group_id: number; // FK → GROUPS.id
  joined_at: Date;
  is_moderator: boolean;
}

export interface GroupMessage {
  id: number;
  group_id: number; // FK → GROUPS.id
  sender_id: number; // FK → USERS.id
  reacted_message_id?: number; // FK → Group Messages.id, only for reaction type transactions
  message_type: MessageType;
  content: string;
  created_at: Date;
}

// Private Messages
export interface PrivateMessage {
  id: number;
  sender_id: number; // FK → USERS.id
  receiver_id: number; // FK → USERS.id
  message_type: MessageType;
  content: string;
  created_at: Date;
}

// Reports
export interface Report {
  id: number;
  reporter_id: number; // FK → USERS.id
  reported_user_id: number; // FK → USERS.id
  message_id?: number; // FK → MESSAGES.id (nullable)
  reason: string;
  created_at: Date;
}

// Blog
export interface Blog {
  id: number;
  topic?: string; // e.g. ek gida
  subject: string;
  content: string;
  created_at: Date;
  language: Language;
}

// Blog Comments
export interface BlogComment {
  id: number;
  blog_id: number; // FK → Blog.id
  parent_comment_id?: number; // NULL for top-level comments, points to parent for replies
  sender_id: number; // FK → USERS.id
  receiver_id: number; // FK → USERS.id
  message_type: MessageType; // like, dislike, reply
  content: string;
  created_at: Date;
}

// Blog Comment Reactions
export interface BlogCommentReaction {
  id: number;
  blog_id: number; // FK → Blog.id
  commented_on: number; // COMMENT.id
  sender_id: number; // FK → USERS.id
  receiver_id: number; // FK → USERS.id - Whom parent comment owns
  message_type: MessageType; // like, dislike, reply
  content: string;
  created_at: Date;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Authentication types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  username: string;
  phone: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
}

// Request/Response DTOs for CRUD operations
export interface CreateUserRequest {
  email: string;
  phone: string;
  username: string;
  password: string;
  bio?: string;
  profile_photo?: string;
  country?: string;
  city?: string;
  user_preferences?: Record<string, any>;
}

export interface UpdateUserRequest {
  email?: string;
  phone?: string;
  username?: string;
  bio?: string;
  profile_photo?: string;
  country?: string;
  city?: string;
  user_preferences?: Record<string, any>;
}

export interface CreateGroupRequest {
  region?: string;
  city?: string;
  country: string;
  birth_year?: number;
  language: Language;
  topic?: string;
  is_public: boolean;
}

export interface UpdateGroupRequest {
  region?: string;
  city?: string;
  country?: string;
  birth_year?: number;
  language?: Language;
  topic?: string;
  is_public?: boolean;
}

export interface CreateGroupMessageRequest {
  group_id: number;
  reacted_message_id?: number;
  message_type: MessageType;
  content: string;
}

export interface CreatePrivateMessageRequest {
  receiver_id: number;
  message_type: MessageType;
  content: string;
}

export interface CreateReportRequest {
  reported_user_id: number;
  message_id?: number;
  reason: string;
}

export interface CreateBlogRequest {
  topic?: string;
  subject: string;
  content: string;
  language: Language;
}

export interface UpdateBlogRequest {
  topic?: string;
  subject?: string;
  content?: string;
  language?: Language;
}

export interface CreateBlogCommentRequest {
  blog_id: number;
  parent_comment_id?: number;
  receiver_id: number;
  message_type: MessageType;
  content: string;
}

export interface CreateBlogCommentReactionRequest {
  blog_id: number;
  commented_on: number;
  receiver_id: number;
  message_type: MessageType;
  content: string;
}
