import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import {
  User,
  CreateUserRequest,
  UpdateUserRequest,
  ApiResponse,
  PaginatedResponse
} from '@canmoms/shared';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private readonly baseUrl = 'http://localhost:3000/api/users';

  constructor(private http: HttpClient) {}

  /**
   * Get all users with pagination
   */
  getAllUsers(page: number = 1, limit: number = 10): Observable<PaginatedResponse<User>> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get<PaginatedResponse<User>>(this.baseUrl, { params });
  }

  /**
   * Get user by ID
   */
  getUserById(id: number): Observable<ApiResponse<User>> {
    return this.http.get<ApiResponse<User>>(`${this.baseUrl}/${id}`);
  }

  /**
   * Create a new user
   */
  createUser(userData: CreateUserRequest): Observable<ApiResponse<User>> {
    return this.http.post<ApiResponse<User>>(this.baseUrl, userData);
  }

  /**
   * Update an existing user
   */
  updateUser(id: number, userData: UpdateUserRequest): Observable<ApiResponse<User>> {
    return this.http.put<ApiResponse<User>>(`${this.baseUrl}/${id}`, userData);
  }

  /**
   * Delete a user
   */
  deleteUser(id: number): Observable<ApiResponse> {
    return this.http.delete<ApiResponse>(`${this.baseUrl}/${id}`);
  }

  /**
   * Ban a user
   */
  banUser(id: number, bannedUntil?: Date): Observable<ApiResponse<User>> {
    const body = bannedUntil ? { banned_until: bannedUntil.toISOString() } : {};
    return this.http.post<ApiResponse<User>>(`${this.baseUrl}/${id}/ban`, body);
  }

  /**
   * Unban a user
   */
  unbanUser(id: number): Observable<ApiResponse<User>> {
    return this.http.post<ApiResponse<User>>(`${this.baseUrl}/${id}/unban`, {});
  }

  /**
   * Search users by username or email
   */
  searchUsers(query: string, page: number = 1, limit: number = 10): Observable<PaginatedResponse<User>> {
    const params = new HttpParams()
      .set('search', query)
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get<PaginatedResponse<User>>(this.baseUrl, { params });
  }

  /**
   * Get user profile (current user)
   */
  getCurrentUserProfile(): Observable<ApiResponse<User>> {
    return this.http.get<ApiResponse<User>>(`${this.baseUrl}/profile`);
  }

  /**
   * Update current user profile
   */
  updateCurrentUserProfile(userData: UpdateUserRequest): Observable<ApiResponse<User>> {
    return this.http.put<ApiResponse<User>>(`${this.baseUrl}/profile`, userData);
  }

  /**
   * Upload profile photo
   */
  uploadProfilePhoto(file: File): Observable<ApiResponse<{ url: string }>> {
    const formData = new FormData();
    formData.append('photo', file);
    
    return this.http.post<ApiResponse<{ url: string }>>(`${this.baseUrl}/profile/photo`, formData);
  }

  /**
   * Verify user account with OTP
   */
  verifyAccount(otpCode: number): Observable<ApiResponse<User>> {
    return this.http.post<ApiResponse<User>>(`${this.baseUrl}/verify`, { otp_code: otpCode });
  }

  /**
   * Request OTP for account verification
   */
  requestOTP(): Observable<ApiResponse> {
    return this.http.post<ApiResponse>(`${this.baseUrl}/request-otp`, {});
  }

  /**
   * Change user password
   */
  changePassword(currentPassword: string, newPassword: string): Observable<ApiResponse> {
    return this.http.post<ApiResponse>(`${this.baseUrl}/change-password`, {
      current_password: currentPassword,
      new_password: newPassword
    });
  }

  /**
   * Get users by country/city for location-based features
   */
  getUsersByLocation(country?: string, city?: string, page: number = 1, limit: number = 10): Observable<PaginatedResponse<User>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    if (country) {
      params = params.set('country', country);
    }

    if (city) {
      params = params.set('city', city);
    }

    return this.http.get<PaginatedResponse<User>>(`${this.baseUrl}/location`, { params });
  }
}
