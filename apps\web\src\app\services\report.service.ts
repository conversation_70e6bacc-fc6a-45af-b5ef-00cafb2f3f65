import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import {
  Report,
  CreateReportRequest,
  ApiResponse,
  PaginatedResponse
} from '@canmoms/shared';

@Injectable({
  providedIn: 'root'
})
export class ReportService {
  private readonly baseUrl = 'http://localhost:3000/api/reports';

  constructor(private http: HttpClient) {}

  /**
   * Get all reports with pagination and filters (admin only)
   */
  getAllReports(
    page: number = 1, 
    limit: number = 10,
    filters?: {
      reporterId?: number;
      reportedUserId?: number;
    }
  ): Observable<PaginatedResponse<Report>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    if (filters) {
      if (filters.reporterId) params = params.set('reporterId', filters.reporterId.toString());
      if (filters.reportedUserId) params = params.set('reportedUserId', filters.reportedUserId.toString());
    }

    return this.http.get<PaginatedResponse<Report>>(this.baseUrl, { params });
  }

  /**
   * Get report by ID
   */
  getReportById(id: number): Observable<ApiResponse<Report>> {
    return this.http.get<ApiResponse<Report>>(`${this.baseUrl}/${id}`);
  }

  /**
   * Create a new report
   */
  createReport(reportData: CreateReportRequest): Observable<ApiResponse<Report>> {
    return this.http.post<ApiResponse<Report>>(this.baseUrl, reportData);
  }

  /**
   * Delete a report
   */
  deleteReport(id: number): Observable<ApiResponse> {
    return this.http.delete<ApiResponse>(`${this.baseUrl}/${id}`);
  }

  /**
   * Get reports for a specific user
   */
  getReportsByUser(userId: number, page: number = 1, limit: number = 10): Observable<PaginatedResponse<Report>> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get<PaginatedResponse<Report>>(`${this.baseUrl}/user/${userId}`, { params });
  }

  /**
   * Get report statistics (admin only)
   */
  getReportStats(): Observable<ApiResponse<{
    totalReports: number;
    uniqueReportedUsers: number;
    uniqueReporters: number;
    mostReportedUsers: Array<{ userId: number; reportCount: number }>;
  }>> {
    return this.http.get<ApiResponse<any>>(`${this.baseUrl}/stats`);
  }

  /**
   * Report a user
   */
  reportUser(reportedUserId: number, reason: string, messageId?: number): Observable<ApiResponse<Report>> {
    return this.createReport({
      reported_user_id: reportedUserId,
      reason,
      message_id: messageId
    });
  }

  /**
   * Report a group message
   */
  reportGroupMessage(reportedUserId: number, messageId: number, reason: string): Observable<ApiResponse<Report>> {
    return this.createReport({
      reported_user_id: reportedUserId,
      message_id: messageId,
      reason
    });
  }

  /**
   * Report a private message
   */
  reportPrivateMessage(reportedUserId: number, messageId: number, reason: string): Observable<ApiResponse<Report>> {
    return this.createReport({
      reported_user_id: reportedUserId,
      message_id: messageId,
      reason
    });
  }

  /**
   * Report inappropriate behavior
   */
  reportInappropriateBehavior(reportedUserId: number, reason: string): Observable<ApiResponse<Report>> {
    return this.createReport({
      reported_user_id: reportedUserId,
      reason
    });
  }

  /**
   * Report spam
   */
  reportSpam(reportedUserId: number, messageId?: number): Observable<ApiResponse<Report>> {
    return this.createReport({
      reported_user_id: reportedUserId,
      message_id: messageId,
      reason: 'Spam content'
    });
  }

  /**
   * Report harassment
   */
  reportHarassment(reportedUserId: number, reason: string, messageId?: number): Observable<ApiResponse<Report>> {
    return this.createReport({
      reported_user_id: reportedUserId,
      message_id: messageId,
      reason: `Harassment: ${reason}`
    });
  }

  /**
   * Report fake profile
   */
  reportFakeProfile(reportedUserId: number, reason: string): Observable<ApiResponse<Report>> {
    return this.createReport({
      reported_user_id: reportedUserId,
      reason: `Fake profile: ${reason}`
    });
  }

  /**
   * Report inappropriate content
   */
  reportInappropriateContent(reportedUserId: number, messageId: number, reason: string): Observable<ApiResponse<Report>> {
    return this.createReport({
      reported_user_id: reportedUserId,
      message_id: messageId,
      reason: `Inappropriate content: ${reason}`
    });
  }

  /**
   * Get my reports (reports created by current user)
   */
  getMyReports(page: number = 1, limit: number = 10): Observable<PaginatedResponse<Report>> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get<PaginatedResponse<Report>>(`${this.baseUrl}/my-reports`, { params });
  }

  /**
   * Get reports against me (reports where current user is reported)
   */
  getReportsAgainstMe(page: number = 1, limit: number = 10): Observable<PaginatedResponse<Report>> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get<PaginatedResponse<Report>>(`${this.baseUrl}/against-me`, { params });
  }

  /**
   * Get pending reports (admin only)
   */
  getPendingReports(page: number = 1, limit: number = 10): Observable<PaginatedResponse<Report>> {
    const params = new HttpParams()
      .set('status', 'pending')
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get<PaginatedResponse<Report>>(this.baseUrl, { params });
  }

  /**
   * Get resolved reports (admin only)
   */
  getResolvedReports(page: number = 1, limit: number = 10): Observable<PaginatedResponse<Report>> {
    const params = new HttpParams()
      .set('status', 'resolved')
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get<PaginatedResponse<Report>>(this.baseUrl, { params });
  }

  /**
   * Mark report as resolved (admin only)
   */
  markReportAsResolved(reportId: number): Observable<ApiResponse<Report>> {
    return this.http.put<ApiResponse<Report>>(`${this.baseUrl}/${reportId}/resolve`, {});
  }

  /**
   * Dismiss report (admin only)
   */
  dismissReport(reportId: number, reason?: string): Observable<ApiResponse> {
    return this.http.put<ApiResponse>(`${this.baseUrl}/${reportId}/dismiss`, { reason });
  }
}
