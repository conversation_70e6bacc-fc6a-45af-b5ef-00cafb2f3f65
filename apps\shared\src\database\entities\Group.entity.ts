import 'reflect-metadata';
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { Language } from '../../types';
import { GroupMember } from './GroupMember.entity';
import { GroupMessage } from './GroupMessage.entity';

@Entity('groups')
export class Group {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 100, nullable: true })
  region?: string;

  @Column({ length: 100, nullable: true })
  city?: string;

  @Column({ length: 100 })
  country: string; // not nullable for central groups

  @Column({ type: 'integer', nullable: true })
  birth_year?: number;

  @Column({
    type: 'enum',
    enum: Language
  })
  language: Language;

  @Column({ length: 200, nullable: true })
  topic?: string; // e.g. ek gida

  @CreateDateColumn({ type: 'timestamp with time zone' })
  created_at: Date;

  @Column({ default: true })
  is_public: boolean;

  @UpdateDateColumn({ type: 'timestamp with time zone' })
  updated_at: Date;

  // Relations
  @OneToMany(() => GroupMember, groupMember => groupMember.group)
  members: GroupMember[];

  @OneToMany(() => GroupMessage, groupMessage => groupMessage.group)
  messages: GroupMessage[];
}
