import { Http } from '@nativescript/core';
import { BehaviorSubject, Observable } from 'rxjs';
import {
  GroupMessage,
  PrivateMessage,
  CreateGroupMessageRequest,
  CreatePrivateMessageRequest,
  ApiResponse,
  PaginatedResponse,
  MessageType
} from '@canmoms/shared';

export class MessagingService {
  private readonly baseUrl = 'http://localhost:3000/api/messaging';
  private unreadCount$ = new BehaviorSubject<number>(0);

  /**
   * Get unread message count observable
   */
  getUnreadCount(): Observable<number> {
    return this.unreadCount$.asObservable();
  }

  /**
   * Update unread count
   */
  private updateUnreadCount(count: number): void {
    this.unreadCount$.next(count);
  }

  // GROUP MESSAGES

  /**
   * Get group messages with pagination
   */
  async getGroupMessages(groupId: number, page: number = 1, limit: number = 50): Promise<PaginatedResponse<GroupMessage>> {
    const url = `${this.baseUrl}/groups/${groupId}/messages?page=${page}&limit=${limit}`;
    const response = await Http.getJSON<PaginatedResponse<GroupMessage>>(url);
    return response;
  }

  /**
   * Send a message to a group
   */
  async sendGroupMessage(groupId: number, messageData: Omit<CreateGroupMessageRequest, 'group_id'>): Promise<ApiResponse<GroupMessage>> {
    const payload: CreateGroupMessageRequest = {
      ...messageData,
      group_id: groupId
    };

    const response = await Http.request({
      url: `${this.baseUrl}/groups/${groupId}/messages`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      content: JSON.stringify(payload)
    });

    return response.content.toJSON() as ApiResponse<GroupMessage>;
  }

  /**
   * Get a specific group message
   */
  async getGroupMessageById(groupId: number, messageId: number): Promise<ApiResponse<GroupMessage>> {
    const url = `${this.baseUrl}/groups/${groupId}/messages/${messageId}`;
    const response = await Http.getJSON<ApiResponse<GroupMessage>>(url);
    return response;
  }

  /**
   * Delete a group message
   */
  async deleteGroupMessage(groupId: number, messageId: number): Promise<ApiResponse> {
    const response = await Http.request({
      url: `${this.baseUrl}/groups/${groupId}/messages/${messageId}`,
      method: 'DELETE'
    });

    return response.content.toJSON() as ApiResponse;
  }

  /**
   * React to a group message
   */
  async reactToGroupMessage(
    groupId: number, 
    messageId: number, 
    messageType: MessageType.LIKE | MessageType.DISLIKE,
    content: string = ''
  ): Promise<ApiResponse<GroupMessage>> {
    return this.sendGroupMessage(groupId, {
      reacted_message_id: messageId,
      message_type: messageType,
      content
    });
  }

  // PRIVATE MESSAGES

  /**
   * Get private messages between current user and another user
   */
  async getPrivateMessages(otherUserId: number, page: number = 1, limit: number = 50): Promise<PaginatedResponse<PrivateMessage>> {
    const url = `${this.baseUrl}/private-messages?otherUserId=${otherUserId}&page=${page}&limit=${limit}`;
    const response = await Http.getJSON<PaginatedResponse<PrivateMessage>>(url);
    return response;
  }

  /**
   * Send a private message
   */
  async sendPrivateMessage(messageData: CreatePrivateMessageRequest): Promise<ApiResponse<PrivateMessage>> {
    const response = await Http.request({
      url: `${this.baseUrl}/private-messages`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      content: JSON.stringify(messageData)
    });

    return response.content.toJSON() as ApiResponse<PrivateMessage>;
  }

  /**
   * Get a specific private message
   */
  async getPrivateMessageById(messageId: number): Promise<ApiResponse<PrivateMessage>> {
    const url = `${this.baseUrl}/private-messages/${messageId}`;
    const response = await Http.getJSON<ApiResponse<PrivateMessage>>(url);
    return response;
  }

  /**
   * Delete a private message
   */
  async deletePrivateMessage(messageId: number): Promise<ApiResponse> {
    const response = await Http.request({
      url: `${this.baseUrl}/private-messages/${messageId}`,
      method: 'DELETE'
    });

    return response.content.toJSON() as ApiResponse;
  }

  /**
   * Get all conversations (list of users current user has messaged with)
   */
  async getConversations(): Promise<ApiResponse<PrivateMessage[]>> {
    const url = `${this.baseUrl}/private-messages/conversations`;
    const response = await Http.getJSON<ApiResponse<PrivateMessage[]>>(url);
    return response;
  }

  // UTILITY METHODS

  /**
   * Send a text message to a group
   */
  async sendTextToGroup(groupId: number, content: string): Promise<ApiResponse<GroupMessage>> {
    return this.sendGroupMessage(groupId, {
      message_type: MessageType.TEXT,
      content
    });
  }

  /**
   * Send a text message privately
   */
  async sendTextPrivately(receiverId: number, content: string): Promise<ApiResponse<PrivateMessage>> {
    return this.sendPrivateMessage({
      receiver_id: receiverId,
      message_type: MessageType.TEXT,
      content
    });
  }

  /**
   * Send an image to a group
   */
  async sendImageToGroup(groupId: number, imageUrl: string, caption?: string): Promise<ApiResponse<GroupMessage>> {
    return this.sendGroupMessage(groupId, {
      message_type: MessageType.IMAGE,
      content: caption || imageUrl
    });
  }

  /**
   * Send an image privately
   */
  async sendImagePrivately(receiverId: number, imageUrl: string, caption?: string): Promise<ApiResponse<PrivateMessage>> {
    return this.sendPrivateMessage({
      receiver_id: receiverId,
      message_type: MessageType.IMAGE,
      content: caption || imageUrl
    });
  }

  /**
   * Send an emoji reaction to a group
   */
  async sendEmojiToGroup(groupId: number, emoji: string): Promise<ApiResponse<GroupMessage>> {
    return this.sendGroupMessage(groupId, {
      message_type: MessageType.EMOJI,
      content: emoji
    });
  }

  /**
   * Send an emoji privately
   */
  async sendEmojiPrivately(receiverId: number, emoji: string): Promise<ApiResponse<PrivateMessage>> {
    return this.sendPrivateMessage({
      receiver_id: receiverId,
      message_type: MessageType.EMOJI,
      content: emoji
    });
  }

  /**
   * Mark messages as read in a conversation
   */
  async markMessagesAsRead(otherUserId: number): Promise<ApiResponse> {
    const response = await Http.request({
      url: `${this.baseUrl}/private-messages/mark-read`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      content: JSON.stringify({
        other_user_id: otherUserId
      })
    });

    return response.content.toJSON() as ApiResponse;
  }

  /**
   * Get unread message count
   */
  async fetchUnreadMessageCount(): Promise<number> {
    try {
      const url = `${this.baseUrl}/private-messages/unread-count`;
      const response = await Http.getJSON<ApiResponse<{ count: number }>>(url);
      
      if (response.success && response.data) {
        this.updateUnreadCount(response.data.count);
        return response.data.count;
      }
      
      return 0;
    } catch (error) {
      console.error('Error fetching unread count:', error);
      return 0;
    }
  }

  /**
   * Search messages in a group
   */
  async searchGroupMessages(groupId: number, query: string, page: number = 1, limit: number = 20): Promise<PaginatedResponse<GroupMessage>> {
    const url = `${this.baseUrl}/groups/${groupId}/messages/search?query=${encodeURIComponent(query)}&page=${page}&limit=${limit}`;
    const response = await Http.getJSON<PaginatedResponse<GroupMessage>>(url);
    return response;
  }

  /**
   * Search private messages with a user
   */
  async searchPrivateMessages(otherUserId: number, query: string, page: number = 1, limit: number = 20): Promise<PaginatedResponse<PrivateMessage>> {
    const url = `${this.baseUrl}/private-messages/search?otherUserId=${otherUserId}&query=${encodeURIComponent(query)}&page=${page}&limit=${limit}`;
    const response = await Http.getJSON<PaginatedResponse<PrivateMessage>>(url);
    return response;
  }

  /**
   * Start periodic unread count updates
   */
  startUnreadCountUpdates(intervalMs: number = 30000): void {
    setInterval(() => {
      this.fetchUnreadMessageCount();
    }, intervalMs);
  }

  /**
   * Stop periodic unread count updates
   */
  stopUnreadCountUpdates(): void {
    // In a real implementation, you would store the interval ID and clear it
    // This is a simplified version
  }
}
