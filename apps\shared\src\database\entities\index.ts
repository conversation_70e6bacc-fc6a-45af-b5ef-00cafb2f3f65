// Export all TypeORM entities with Entity suffix to avoid naming conflicts
export { User as UserEntity } from './User.entity';
export { Group as GroupEntity } from './Group.entity';
export { GroupMember as GroupMemberEntity } from './GroupMember.entity';
export { GroupMessage as GroupMessageEntity } from './GroupMessage.entity';
export { PrivateMessage as PrivateMessageEntity } from './PrivateMessage.entity';
export { Report as ReportEntity } from './Report.entity';
export { Blog as BlogEntity } from './Blog.entity';
export { BlogComment as BlogCommentEntity } from './BlogComment.entity';
export { BlogCommentReaction as BlogCommentReactionEntity } from './BlogCommentReaction.entity';

// Import entities for the array
import { User } from './User.entity';
import { Group } from './Group.entity';
import { GroupMember } from './GroupMember.entity';
import { GroupMessage } from './GroupMessage.entity';
import { PrivateMessage } from './PrivateMessage.entity';
import { Report } from './Report.entity';
import { Blog } from './Blog.entity';
import { BlogComment } from './BlogComment.entity';
import { BlogCommentReaction } from './BlogCommentReaction.entity';

// Array of all entities for TypeORM configuration
export const entities = [
  User,
  Group,
  GroupMember,
  GroupMessage,
  PrivateMessage,
  Report,
  Blog,
  BlogComment,
  BlogCommentReaction
];
