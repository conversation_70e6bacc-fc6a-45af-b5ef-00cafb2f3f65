import 'reflect-metadata';
import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, OneToMany } from 'typeorm';
import { Language } from '../../types';
import { User } from './User.entity';
import { BlogComment } from './BlogComment.entity';

@Entity('blogs')
export class Blog {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 200, nullable: true })
  topic?: string; // e.g. ek gida

  @Column({ length: 500 })
  subject: string;

  @Column({ type: 'text' })
  content: string;

  @CreateDateColumn({ type: 'timestamp with time zone' })
  created_at: Date;

  @Column({
    type: 'enum',
    enum: Language
  })
  language: Language;

  @Column()
  author_id: number;

  @UpdateDateColumn({ type: 'timestamp with time zone' })
  updated_at: Date;

  @Column({ default: true })
  is_published: boolean;

  @Column({ default: 0 })
  view_count: number;

  // Relations
  @ManyToOne(() => User, user => user.blogs, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'author_id' })
  author: User;

  @OneToMany(() => BlogComment, blogComment => blogComment.blog)
  comments: BlogComment[];
}
