import express from 'express';
import cors from 'cors';
import itemRoutes from './routes/itemRoutes';
import authRoutes from './routes/authRoutes';
import userRoutes from './routes/userRoutes';
import groupRoutes from './routes/groupRoutes';
import messagingRoutes from './routes/messagingRoutes';
import blogRoutes from './routes/blogRoutes';
import reportRoutes from './routes/reportRoutes';
import { errorHandler } from './middlewares/errorHandler';

const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/api/items', itemRoutes);
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/groups', groupRoutes);
app.use('/api/messaging', messagingRoutes);
app.use('/api/blogs', blogRoutes);
app.use('/api/reports', reportRoutes);

// Global error handler (should be after routes)
app.use(errorHandler);

export default app;