import { Http } from '@nativescript/core';
import {
  Report,
  CreateReportRequest,
  ApiResponse,
  PaginatedResponse
} from '@canmoms/shared';

export class ReportService {
  private readonly baseUrl = 'http://localhost:3000/api/reports';

  /**
   * Get all reports with pagination and filters (admin only)
   */
  async getAllReports(
    page: number = 1, 
    limit: number = 10,
    filters?: {
      reporterId?: number;
      reportedUserId?: number;
    }
  ): Promise<PaginatedResponse<Report>> {
    let url = `${this.baseUrl}?page=${page}&limit=${limit}`;

    if (filters) {
      if (filters.reporterId) url += `&reporterId=${filters.reporterId}`;
      if (filters.reportedUserId) url += `&reportedUserId=${filters.reportedUserId}`;
    }

    const response = await Http.getJSON<PaginatedResponse<Report>>(url);
    return response;
  }

  /**
   * Get report by ID
   */
  async getReportById(id: number): Promise<ApiResponse<Report>> {
    const url = `${this.baseUrl}/${id}`;
    const response = await Http.getJSON<ApiResponse<Report>>(url);
    return response;
  }

  /**
   * Create a new report
   */
  async createReport(reportData: CreateReportRequest): Promise<ApiResponse<Report>> {
    const response = await Http.request({
      url: this.baseUrl,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      content: JSON.stringify(reportData)
    });

    return response.content.toJSON() as ApiResponse<Report>;
  }

  /**
   * Delete a report
   */
  async deleteReport(id: number): Promise<ApiResponse> {
    const response = await Http.request({
      url: `${this.baseUrl}/${id}`,
      method: 'DELETE'
    });

    return response.content.toJSON() as ApiResponse;
  }

  /**
   * Get reports for a specific user
   */
  async getReportsByUser(userId: number, page: number = 1, limit: number = 10): Promise<PaginatedResponse<Report>> {
    const url = `${this.baseUrl}/user/${userId}?page=${page}&limit=${limit}`;
    const response = await Http.getJSON<PaginatedResponse<Report>>(url);
    return response;
  }

  /**
   * Get report statistics (admin only)
   */
  async getReportStats(): Promise<ApiResponse<{
    totalReports: number;
    uniqueReportedUsers: number;
    uniqueReporters: number;
    mostReportedUsers: Array<{ userId: number; reportCount: number }>;
  }>> {
    const url = `${this.baseUrl}/stats`;
    const response = await Http.getJSON<ApiResponse<any>>(url);
    return response;
  }

  /**
   * Report a user
   */
  async reportUser(reportedUserId: number, reason: string, messageId?: number): Promise<ApiResponse<Report>> {
    return this.createReport({
      reported_user_id: reportedUserId,
      reason,
      message_id: messageId
    });
  }

  /**
   * Report a group message
   */
  async reportGroupMessage(reportedUserId: number, messageId: number, reason: string): Promise<ApiResponse<Report>> {
    return this.createReport({
      reported_user_id: reportedUserId,
      message_id: messageId,
      reason
    });
  }

  /**
   * Report a private message
   */
  async reportPrivateMessage(reportedUserId: number, messageId: number, reason: string): Promise<ApiResponse<Report>> {
    return this.createReport({
      reported_user_id: reportedUserId,
      message_id: messageId,
      reason
    });
  }

  /**
   * Report inappropriate behavior
   */
  async reportInappropriateBehavior(reportedUserId: number, reason: string): Promise<ApiResponse<Report>> {
    return this.createReport({
      reported_user_id: reportedUserId,
      reason
    });
  }

  /**
   * Report spam
   */
  async reportSpam(reportedUserId: number, messageId?: number): Promise<ApiResponse<Report>> {
    return this.createReport({
      reported_user_id: reportedUserId,
      message_id: messageId,
      reason: 'Spam content'
    });
  }

  /**
   * Report harassment
   */
  async reportHarassment(reportedUserId: number, reason: string, messageId?: number): Promise<ApiResponse<Report>> {
    return this.createReport({
      reported_user_id: reportedUserId,
      message_id: messageId,
      reason: `Harassment: ${reason}`
    });
  }

  /**
   * Report fake profile
   */
  async reportFakeProfile(reportedUserId: number, reason: string): Promise<ApiResponse<Report>> {
    return this.createReport({
      reported_user_id: reportedUserId,
      reason: `Fake profile: ${reason}`
    });
  }

  /**
   * Report inappropriate content
   */
  async reportInappropriateContent(reportedUserId: number, messageId: number, reason: string): Promise<ApiResponse<Report>> {
    return this.createReport({
      reported_user_id: reportedUserId,
      message_id: messageId,
      reason: `Inappropriate content: ${reason}`
    });
  }

  /**
   * Get my reports (reports created by current user)
   */
  async getMyReports(page: number = 1, limit: number = 10): Promise<PaginatedResponse<Report>> {
    const url = `${this.baseUrl}/my-reports?page=${page}&limit=${limit}`;
    const response = await Http.getJSON<PaginatedResponse<Report>>(url);
    return response;
  }

  /**
   * Get reports against me (reports where current user is reported)
   */
  async getReportsAgainstMe(page: number = 1, limit: number = 10): Promise<PaginatedResponse<Report>> {
    const url = `${this.baseUrl}/against-me?page=${page}&limit=${limit}`;
    const response = await Http.getJSON<PaginatedResponse<Report>>(url);
    return response;
  }

  /**
   * Get pending reports (admin only)
   */
  async getPendingReports(page: number = 1, limit: number = 10): Promise<PaginatedResponse<Report>> {
    const url = `${this.baseUrl}?status=pending&page=${page}&limit=${limit}`;
    const response = await Http.getJSON<PaginatedResponse<Report>>(url);
    return response;
  }

  /**
   * Get resolved reports (admin only)
   */
  async getResolvedReports(page: number = 1, limit: number = 10): Promise<PaginatedResponse<Report>> {
    const url = `${this.baseUrl}?status=resolved&page=${page}&limit=${limit}`;
    const response = await Http.getJSON<PaginatedResponse<Report>>(url);
    return response;
  }

  /**
   * Mark report as resolved (admin only)
   */
  async markReportAsResolved(reportId: number): Promise<ApiResponse<Report>> {
    const response = await Http.request({
      url: `${this.baseUrl}/${reportId}/resolve`,
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      content: JSON.stringify({})
    });

    return response.content.toJSON() as ApiResponse<Report>;
  }

  /**
   * Dismiss report (admin only)
   */
  async dismissReport(reportId: number, reason?: string): Promise<ApiResponse> {
    const response = await Http.request({
      url: `${this.baseUrl}/${reportId}/dismiss`,
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      content: JSON.stringify({ reason })
    });

    return response.content.toJSON() as ApiResponse;
  }

  /**
   * Quick report with predefined reasons
   */
  async quickReport(reportedUserId: number, reportType: 'spam' | 'harassment' | 'fake' | 'inappropriate', messageId?: number): Promise<ApiResponse<Report>> {
    const reasons = {
      spam: 'Spam content',
      harassment: 'Harassment or bullying',
      fake: 'Fake profile or impersonation',
      inappropriate: 'Inappropriate content or behavior'
    };

    return this.createReport({
      reported_user_id: reportedUserId,
      message_id: messageId,
      reason: reasons[reportType]
    });
  }

  /**
   * Bulk report multiple messages from same user
   */
  async bulkReportMessages(reportedUserId: number, messageIds: number[], reason: string): Promise<ApiResponse<Report[]>> {
    const reports = messageIds.map(messageId => ({
      reported_user_id: reportedUserId,
      message_id: messageId,
      reason
    }));

    const response = await Http.request({
      url: `${this.baseUrl}/bulk`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      content: JSON.stringify({ reports })
    });

    return response.content.toJSON() as ApiResponse<Report[]>;
  }
}
