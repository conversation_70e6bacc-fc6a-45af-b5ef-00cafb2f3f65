import { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { Repository } from 'typeorm';
import { AppDataSource } from '../config/database';
import {
  UserEntity,
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  ApiResponse,
  CreateUserRequest
} from '@canmoms/shared';

// Get repository helper
const getUserRepository = (): Repository<UserEntity> => {
  return AppDataSource.getRepository(UserEntity);
};

// Generate JWT token
const generateToken = (user: UserEntity): string => {
  const jwtSecret = process.env.JWT_SECRET || 'your-super-secret-jwt-key';
  const expiresIn = process.env.JWT_EXPIRES_IN || '24h';

  return jwt.sign(
    {
      userId: user.id,
      email: user.email,
      is_admin: user.is_admin
    },
    jwtSecret,
    { expiresIn: expiresIn }
  );
};

// Generate OTP
const generateOTP = (): number => {
  return Math.floor(100000 + Math.random() * 900000); // 6-digit OTP
};

export const authController = {
  // POST /api/auth/register
  register: async (req: Request, res: Response): Promise<void> => {
    try {
      const userData: RegisterRequest = req.body;
      const userRepository = getUserRepository();

      // Check if email or username already exists
      const existingUser = await userRepository.findOne({
        where: [
          { email: userData.email },
          { username: userData.username }
        ]
      });

      if (existingUser) {
        const response: ApiResponse = {
          success: false,
          error: 'User with this email or username already exists'
        };
        res.status(400).json(response);
        return;
      }

      // Hash password
      const saltRounds = 12;
      const hashedPassword = await bcrypt.hash(userData.password, saltRounds);

      // Generate OTP for verification
      const otpCode = generateOTP();
      const otpExpiration = new Date();
      otpExpiration.setMinutes(otpExpiration.getMinutes() + 15); // 15 minutes

      // Create new user
      const newUser = userRepository.create({
        email: userData.email,
        phone: userData.phone,
        username: userData.username,
        password: hashedPassword,
        otp_code: otpCode,
        otp_expiretion_date: otpExpiration,
        is_banned: false,
        is_admin: false,
        is_verified: false,
        user_preferences: {}
      });

      const savedUser = await userRepository.save(newUser);

      // Generate token
      const token = generateToken(savedUser);

      // TODO: Send OTP via email/SMS
      console.log(`OTP for ${savedUser.email}: ${otpCode}`);

      const response: AuthResponse = {
        user: savedUser,
        token,
        refreshToken: token // In production, implement separate refresh token
      };

      res.status(201).json({
        success: true,
        data: response,
        message: 'User registered successfully. Please verify your account with the OTP sent to your email.'
      });
    } catch (error) {
      console.error('Registration error:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to register user'
      };
      res.status(500).json(response);
    }
  },

  // POST /api/auth/login
  login: async (req: Request, res: Response): Promise<void> => {
    try {
      const { email, password }: LoginRequest = req.body;
      const userRepository = getUserRepository();

      // Find user by email
      const user = await userRepository.findOne({
        where: { email }
      });

      if (!user) {
        const response: ApiResponse = {
          success: false,
          error: 'Invalid email or password'
        };
        res.status(401).json(response);
        return;
      }

      // Check password
      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        const response: ApiResponse = {
          success: false,
          error: 'Invalid email or password'
        };
        res.status(401).json(response);
        return;
      }

      // Check if user is banned
      if (user.is_banned) {
        const now = new Date();
        if (!user.banned_until || user.banned_until > now) {
          const response: ApiResponse = {
            success: false,
            error: 'Your account has been banned'
          };
          res.status(403).json(response);
          return;
        } else {
          // Unban user if ban period has expired
          await userRepository.update(user.id, {
            is_banned: false,
            banned_until: undefined
          });
          user.is_banned = false;
          user.banned_until = undefined;
        }
      }

      // Generate token
      const token = generateToken(user);

      const response: AuthResponse = {
        user,
        token,
        refreshToken: token // In production, implement separate refresh token
      };

      res.json({
        success: true,
        data: response,
        message: 'Login successful'
      });
    } catch (error) {
      console.error('Login error:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to login'
      };
      res.status(500).json(response);
    }
  },

  // POST /api/auth/verify
  verifyAccount: async (req: Request, res: Response): Promise<void> => {
    try {
      const { otp_code } = req.body;
      const userId = req.userId; // From auth middleware

      if (!userId) {
        const response: ApiResponse = {
          success: false,
          error: 'Authentication required'
        };
        res.status(401).json(response);
        return;
      }

      const userRepository = getUserRepository();
      const user = await userRepository.findOne({
        where: { id: userId }
      });

      if (!user) {
        const response: ApiResponse = {
          success: false,
          error: 'User not found'
        };
        res.status(404).json(response);
        return;
      }

      // Check OTP
      const now = new Date();
      if (!user.otp_code || user.otp_code !== otp_code) {
        const response: ApiResponse = {
          success: false,
          error: 'Invalid OTP code'
        };
        res.status(400).json(response);
        return;
      }

      if (!user.otp_expiretion_date || user.otp_expiretion_date < now) {
        const response: ApiResponse = {
          success: false,
          error: 'OTP code has expired'
        };
        res.status(400).json(response);
        return;
      }

      // Verify user
      await userRepository.update(userId, {
        is_verified: true,
        otp_code: undefined,
        otp_expiretion_date: undefined
      });

      const updatedUser = await userRepository.findOne({
        where: { id: userId }
      });

      res.json({
        success: true,
        data: updatedUser,
        message: 'Account verified successfully'
      });
    } catch (error) {
      console.error('Verification error:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to verify account'
      };
      res.status(500).json(response);
    }
  },

  // POST /api/auth/request-otp
  requestOTP: async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.userId;

      if (!userId) {
        const response: ApiResponse = {
          success: false,
          error: 'Authentication required'
        };
        res.status(401).json(response);
        return;
      }

      const userRepository = getUserRepository();
      const otpCode = generateOTP();
      const otpExpiration = new Date();
      otpExpiration.setMinutes(otpExpiration.getMinutes() + 15);

      await userRepository.update(userId, {
        otp_code: otpCode,
        otp_expiretion_date: otpExpiration
      });

      // TODO: Send OTP via email/SMS
      console.log(`New OTP for user ${userId}: ${otpCode}`);

      res.json({
        success: true,
        message: 'OTP sent successfully'
      });
    } catch (error) {
      console.error('OTP request error:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to send OTP'
      };
      res.status(500).json(response);
    }
  },

  // GET /api/auth/me
  getCurrentUser: async (req: Request, res: Response): Promise<void> => {
    try {
      const user = req.user;

      if (!user) {
        const response: ApiResponse = {
          success: false,
          error: 'User not found'
        };
        res.status(404).json(response);
        return;
      }

      res.json({
        success: true,
        data: user
      });
    } catch (error) {
      console.error('Get current user error:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to get user information'
      };
      res.status(500).json(response);
    }
  }
};
