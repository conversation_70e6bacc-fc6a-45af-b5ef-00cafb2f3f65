import { Router } from 'express';
import { messagingController } from '../controllers/messagingController';
import { authenticateToken } from '../middlewares/auth.middleware';

const router = Router();

// All messaging routes require authentication
router.use(authenticateToken);

// Group messages
router.get('/groups/:groupId/messages', messagingController.getGroupMessages);
router.post('/groups/:groupId/messages', messagingController.createGroupMessage);
router.get('/groups/:groupId/messages/:messageId', messagingController.getGroupMessageById);
router.delete('/groups/:groupId/messages/:messageId', messagingController.deleteGroupMessage);

// Private messages
router.get('/private-messages', messagingController.getPrivateMessages);
router.post('/private-messages', messagingController.createPrivateMessage);
router.get('/private-messages/:messageId', messagingController.getPrivateMessageById);
router.delete('/private-messages/:messageId', messagingController.deletePrivateMessage);
router.get('/private-messages/conversations', messagingController.getConversations);

export default router;
