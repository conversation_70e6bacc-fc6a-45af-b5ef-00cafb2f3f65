import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import {
  GroupMessage,
  PrivateMessage,
  CreateGroupMessageRequest,
  CreatePrivateMessageRequest,
  ApiResponse,
  PaginatedResponse,
  MessageType
} from '@canmoms/shared';

@Injectable({
  providedIn: 'root'
})
export class MessagingService {
  private readonly baseUrl = 'http://localhost:3000/api/messaging';

  constructor(private http: HttpClient) {}

  // GROUP MESSAGES

  /**
   * Get group messages with pagination
   */
  getGroupMessages(groupId: number, page: number = 1, limit: number = 50): Observable<PaginatedResponse<GroupMessage>> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get<PaginatedResponse<GroupMessage>>(`${this.baseUrl}/groups/${groupId}/messages`, { params });
  }

  /**
   * Send a message to a group
   */
  sendGroupMessage(groupId: number, messageData: Omit<CreateGroupMessageRequest, 'group_id'>): Observable<ApiResponse<GroupMessage>> {
    const payload: CreateGroupMessageRequest = {
      ...messageData,
      group_id: groupId
    };
    return this.http.post<ApiResponse<GroupMessage>>(`${this.baseUrl}/groups/${groupId}/messages`, payload);
  }

  /**
   * Get a specific group message
   */
  getGroupMessageById(groupId: number, messageId: number): Observable<ApiResponse<GroupMessage>> {
    return this.http.get<ApiResponse<GroupMessage>>(`${this.baseUrl}/groups/${groupId}/messages/${messageId}`);
  }

  /**
   * Delete a group message
   */
  deleteGroupMessage(groupId: number, messageId: number): Observable<ApiResponse> {
    return this.http.delete<ApiResponse>(`${this.baseUrl}/groups/${groupId}/messages/${messageId}`);
  }

  /**
   * React to a group message
   */
  reactToGroupMessage(
    groupId: number, 
    messageId: number, 
    messageType: MessageType.LIKE | MessageType.DISLIKE,
    content: string = ''
  ): Observable<ApiResponse<GroupMessage>> {
    return this.sendGroupMessage(groupId, {
      reacted_message_id: messageId,
      message_type: messageType,
      content
    });
  }

  // PRIVATE MESSAGES

  /**
   * Get private messages between current user and another user
   */
  getPrivateMessages(otherUserId: number, page: number = 1, limit: number = 50): Observable<PaginatedResponse<PrivateMessage>> {
    const params = new HttpParams()
      .set('otherUserId', otherUserId.toString())
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get<PaginatedResponse<PrivateMessage>>(`${this.baseUrl}/private-messages`, { params });
  }

  /**
   * Send a private message
   */
  sendPrivateMessage(messageData: CreatePrivateMessageRequest): Observable<ApiResponse<PrivateMessage>> {
    return this.http.post<ApiResponse<PrivateMessage>>(`${this.baseUrl}/private-messages`, messageData);
  }

  /**
   * Get a specific private message
   */
  getPrivateMessageById(messageId: number): Observable<ApiResponse<PrivateMessage>> {
    return this.http.get<ApiResponse<PrivateMessage>>(`${this.baseUrl}/private-messages/${messageId}`);
  }

  /**
   * Delete a private message
   */
  deletePrivateMessage(messageId: number): Observable<ApiResponse> {
    return this.http.delete<ApiResponse>(`${this.baseUrl}/private-messages/${messageId}`);
  }

  /**
   * Get all conversations (list of users current user has messaged with)
   */
  getConversations(): Observable<ApiResponse<PrivateMessage[]>> {
    return this.http.get<ApiResponse<PrivateMessage[]>>(`${this.baseUrl}/private-messages/conversations`);
  }

  // UTILITY METHODS

  /**
   * Send a text message to a group
   */
  sendTextToGroup(groupId: number, content: string): Observable<ApiResponse<GroupMessage>> {
    return this.sendGroupMessage(groupId, {
      message_type: MessageType.TEXT,
      content
    });
  }

  /**
   * Send a text message privately
   */
  sendTextPrivately(receiverId: number, content: string): Observable<ApiResponse<PrivateMessage>> {
    return this.sendPrivateMessage({
      receiver_id: receiverId,
      message_type: MessageType.TEXT,
      content
    });
  }

  /**
   * Send an image to a group
   */
  sendImageToGroup(groupId: number, imageUrl: string, caption?: string): Observable<ApiResponse<GroupMessage>> {
    return this.sendGroupMessage(groupId, {
      message_type: MessageType.IMAGE,
      content: caption || imageUrl
    });
  }

  /**
   * Send an image privately
   */
  sendImagePrivately(receiverId: number, imageUrl: string, caption?: string): Observable<ApiResponse<PrivateMessage>> {
    return this.sendPrivateMessage({
      receiver_id: receiverId,
      message_type: MessageType.IMAGE,
      content: caption || imageUrl
    });
  }

  /**
   * Send an emoji reaction to a group
   */
  sendEmojiToGroup(groupId: number, emoji: string): Observable<ApiResponse<GroupMessage>> {
    return this.sendGroupMessage(groupId, {
      message_type: MessageType.EMOJI,
      content: emoji
    });
  }

  /**
   * Send an emoji privately
   */
  sendEmojiPrivately(receiverId: number, emoji: string): Observable<ApiResponse<PrivateMessage>> {
    return this.sendPrivateMessage({
      receiver_id: receiverId,
      message_type: MessageType.EMOJI,
      content: emoji
    });
  }

  /**
   * Mark messages as read in a conversation
   */
  markMessagesAsRead(otherUserId: number): Observable<ApiResponse> {
    return this.http.post<ApiResponse>(`${this.baseUrl}/private-messages/mark-read`, {
      other_user_id: otherUserId
    });
  }

  /**
   * Get unread message count
   */
  getUnreadMessageCount(): Observable<ApiResponse<{ count: number }>> {
    return this.http.get<ApiResponse<{ count: number }>>(`${this.baseUrl}/private-messages/unread-count`);
  }

  /**
   * Search messages in a group
   */
  searchGroupMessages(groupId: number, query: string, page: number = 1, limit: number = 20): Observable<PaginatedResponse<GroupMessage>> {
    const params = new HttpParams()
      .set('query', query)
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get<PaginatedResponse<GroupMessage>>(`${this.baseUrl}/groups/${groupId}/messages/search`, { params });
  }

  /**
   * Search private messages with a user
   */
  searchPrivateMessages(otherUserId: number, query: string, page: number = 1, limit: number = 20): Observable<PaginatedResponse<PrivateMessage>> {
    const params = new HttpParams()
      .set('otherUserId', otherUserId.toString())
      .set('query', query)
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get<PaginatedResponse<PrivateMessage>>(`${this.baseUrl}/private-messages/search`, { params });
  }
}
