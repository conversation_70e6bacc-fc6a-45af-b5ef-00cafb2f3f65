import 'reflect-metadata';
import app from './app';
import config from './config/config';
import { initializeDatabase } from './config/database';

const startServer = async () => {
  try {
    // Initialize database connection
    await initializeDatabase();

    app.listen(config.port, () => {
      console.log(`🚀 Server is running on port ${config.port}`);
      console.log(`📊 Database connected successfully`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

startServer();