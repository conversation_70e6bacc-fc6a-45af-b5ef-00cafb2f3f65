import 'reflect-metadata';
import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne, JoinColumn, Unique } from 'typeorm';
import { User } from './User.entity';
import { Group } from './Group.entity';

@Entity('group_members')
@Unique(['user_id', 'group_id']) // Prevent duplicate memberships
export class GroupMember {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  user_id: number;

  @Column()
  group_id: number;

  @CreateDateColumn({ type: 'timestamp with time zone' })
  joined_at: Date;

  @Column({ default: false })
  is_moderator: boolean;

  // Relations
  @ManyToOne(() => User, user => user.groupMemberships, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => Group, group => group.members, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'group_id' })
  group: Group;
}
