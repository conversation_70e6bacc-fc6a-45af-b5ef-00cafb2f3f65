import { Request, Response } from 'express';
import { Repository } from 'typeorm';
import { AppDataSource } from '../config/database';
import {
  Report,
  CreateReportRequest,
  ApiResponse,
  PaginatedResponse,
  ReportEntity
} from '@canmoms/shared';

// Get repository helper
const getReportRepository = (): Repository<ReportEntity> => {
  return AppDataSource.getRepository(ReportEntity);
};

export const reportController = {
  // GET /api/reports (Admin only)
  getAllReports: async (req: Request, res: Response): Promise<void> => {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const skip = (page - 1) * limit;
      const reporterId = req.query.reporterId ? parseInt(req.query.reporterId as string) : undefined;
      const reportedUserId = req.query.reportedUserId ? parseInt(req.query.reportedUserId as string) : undefined;
      const status = req.query.status as string;

      const reportRepository = getReportRepository();
      const queryBuilder = reportRepository.createQueryBuilder('report')
        .leftJoinAndSelect('report.reporter', 'reporter')
        .leftJoinAndSelect('report.reportedUser', 'reportedUser')
        .leftJoinAndSelect('report.resolver', 'resolver');

      if (reporterId) {
        queryBuilder.andWhere('report.reporter_id = :reporterId', { reporterId });
      }

      if (reportedUserId) {
        queryBuilder.andWhere('report.reported_user_id = :reportedUserId', { reportedUserId });
      }

      if (status) {
        queryBuilder.andWhere('report.status = :status', { status });
      }

      const [reports, total] = await queryBuilder
        .skip(skip)
        .take(limit)
        .orderBy('report.created_at', 'DESC')
        .getManyAndCount();

      const totalPages = Math.ceil(total / limit);

      const response: PaginatedResponse<Report> = {
        success: true,
        data: reports,
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      };

      res.json(response);
    } catch (error) {
      console.error('Error fetching reports:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to fetch reports'
      };
      res.status(500).json(response);
    }
  },

  // GET /api/reports/:id
  getReportById: async (req: Request, res: Response): Promise<void> => {
    try {
      const id = parseInt(req.params.id);
      const report = reports.find(r => r.id === id);

      if (!report) {
        const response: ApiResponse = {
          success: false,
          error: 'Report not found'
        };
        res.status(404).json(response);
        return;
      }

      const response: ApiResponse<Report> = {
        success: true,
        data: report
      };

      res.json(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: 'Failed to fetch report'
      };
      res.status(500).json(response);
    }
  },

  // POST /api/reports
  createReport: async (req: Request, res: Response): Promise<void> => {
    try {
      const reportData: CreateReportRequest = req.body;
      const reporterId = parseInt(req.headers['user-id'] as string); // Assuming user ID from auth middleware

      // Check if user is trying to report themselves
      if (reporterId === reportData.reported_user_id) {
        const response: ApiResponse = {
          success: false,
          error: 'You cannot report yourself'
        };
        res.status(400).json(response);
        return;
      }

      // Check if user has already reported this user for the same message
      const existingReport = reports.find(r => 
        r.reporter_id === reporterId && 
        r.reported_user_id === reportData.reported_user_id &&
        r.message_id === reportData.message_id
      );

      if (existingReport) {
        const response: ApiResponse = {
          success: false,
          error: 'You have already reported this user for this message'
        };
        res.status(400).json(response);
        return;
      }

      const newReport: Report = {
        id: nextReportId++,
        reporter_id: reporterId,
        reported_user_id: reportData.reported_user_id,
        message_id: reportData.message_id,
        reason: reportData.reason,
        created_at: new Date()
      };

      reports.push(newReport);

      const response: ApiResponse<Report> = {
        success: true,
        data: newReport,
        message: 'Report submitted successfully'
      };

      res.status(201).json(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: 'Failed to create report'
      };
      res.status(500).json(response);
    }
  },

  // DELETE /api/reports/:id
  deleteReport: async (req: Request, res: Response): Promise<void> => {
    try {
      const id = parseInt(req.params.id);
      const userId = parseInt(req.headers['user-id'] as string);
      const isAdmin = req.headers['is-admin'] === 'true'; // Assuming admin status from auth middleware

      const reportIndex = reports.findIndex(r => r.id === id);

      if (reportIndex === -1) {
        const response: ApiResponse = {
          success: false,
          error: 'Report not found'
        };
        res.status(404).json(response);
        return;
      }

      const report = reports[reportIndex];

      // Only the reporter or an admin can delete a report
      if (report.reporter_id !== userId && !isAdmin) {
        const response: ApiResponse = {
          success: false,
          error: 'Unauthorized to delete this report'
        };
        res.status(403).json(response);
        return;
      }

      reports.splice(reportIndex, 1);

      const response: ApiResponse = {
        success: true,
        message: 'Report deleted successfully'
      };

      res.json(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: 'Failed to delete report'
      };
      res.status(500).json(response);
    }
  },

  // GET /api/reports/user/:userId
  getReportsByUser: async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = parseInt(req.params.userId);
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;

      const userReports = reports.filter(r => r.reported_user_id === userId);
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedReports = userReports.slice(startIndex, endIndex);
      const totalPages = Math.ceil(userReports.length / limit);

      const response: PaginatedResponse<Report> = {
        success: true,
        data: paginatedReports,
        pagination: {
          page,
          limit,
          total: userReports.length,
          totalPages
        }
      };

      res.json(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: 'Failed to fetch user reports'
      };
      res.status(500).json(response);
    }
  },

  // GET /api/reports/stats
  getReportStats: async (req: Request, res: Response): Promise<void> => {
    try {
      const totalReports = reports.length;
      const uniqueReportedUsers = new Set(reports.map(r => r.reported_user_id)).size;
      const uniqueReporters = new Set(reports.map(r => r.reporter_id)).size;
      
      // Get most reported users
      const reportCounts = reports.reduce((acc, report) => {
        acc[report.reported_user_id] = (acc[report.reported_user_id] || 0) + 1;
        return acc;
      }, {} as Record<number, number>);

      const mostReportedUsers = Object.entries(reportCounts)
        .map(([userId, count]) => ({ userId: parseInt(userId), reportCount: count }))
        .sort((a, b) => b.reportCount - a.reportCount)
        .slice(0, 10);

      const stats = {
        totalReports,
        uniqueReportedUsers,
        uniqueReporters,
        mostReportedUsers
      };

      const response: ApiResponse<typeof stats> = {
        success: true,
        data: stats
      };

      res.json(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: 'Failed to fetch report statistics'
      };
      res.status(500).json(response);
    }
  }
};
