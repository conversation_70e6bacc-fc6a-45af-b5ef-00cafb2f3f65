import 'reflect-metadata';
import { <PERSON>tity, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { MessageType } from '../../types';
import { User } from './User.entity';
import { Blog } from './Blog.entity';
import { BlogComment } from './BlogComment.entity';

@Entity('blog_comment_reactions')
export class BlogCommentReaction {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  blog_id: number;

  @Column()
  commented_on: number;

  @Column()
  sender_id: number;

  @Column()
  receiver_id: number;

  @Column({
    type: 'enum',
    enum: MessageType
  })
  message_type: MessageType;

  @Column({ type: 'text' })
  content: string;

  @CreateDateColumn({ type: 'timestamp with time zone' })
  created_at: Date;

  // Relations
  @ManyToOne(() => Blog, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'blog_id' })
  blog: Blog;

  @ManyToOne(() => BlogComment, blogComment => blogComment.reactions, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'commented_on' })
  comment: BlogComment;

  @ManyToOne(() => User, user => user.blogCommentReactions, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'sender_id' })
  sender: User;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'receiver_id' })
  receiver: User;
}
