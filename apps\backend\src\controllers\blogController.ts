import { Request, Response } from 'express';
import { Repository } from 'typeorm';
import { AppDataSource } from '../config/database';
import {
  Blog,
  BlogComment,
  BlogCommentReaction,
  CreateBlogRequest,
  UpdateBlogRequest,
  CreateBlogCommentRequest,
  CreateBlogCommentReactionRequest,
  ApiResponse,
  PaginatedResponse,
  BlogEntity,
  BlogCommentEntity,
  BlogCommentReactionEntity
} from '@canmoms/shared';

// Get repository helpers
const getBlogRepository = (): Repository<BlogEntity> => {
  return AppDataSource.getRepository(BlogEntity);
};

const getBlogCommentRepository = (): Repository<BlogCommentEntity> => {
  return AppDataSource.getRepository(BlogCommentEntity);
};

const getBlogCommentReactionRepository = (): Repository<BlogCommentReactionEntity> => {
  return AppDataSource.getRepository(BlogCommentReactionEntity);
};

export const blogController = {
  // BLOG POSTS

  // GET /api/blogs
  getAllBlogs: async (req: Request, res: Response): Promise<void> => {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const skip = (page - 1) * limit;
      const topic = req.query.topic as string;
      const language = req.query.language as string;

      const blogRepository = getBlogRepository();
      const queryBuilder = blogRepository.createQueryBuilder('blog')
        .leftJoinAndSelect('blog.author', 'author');

      if (topic) {
        queryBuilder.andWhere('blog.topic ILIKE :topic', { topic: `%${topic}%` });
      }

      if (language) {
        queryBuilder.andWhere('blog.language = :language', { language });
      }

      queryBuilder.andWhere('blog.is_published = :isPublished', { isPublished: true });

      const [blogs, total] = await queryBuilder
        .skip(skip)
        .take(limit)
        .orderBy('blog.created_at', 'DESC')
        .getManyAndCount();

      const totalPages = Math.ceil(total / limit);

      const response: PaginatedResponse<Blog> = {
        success: true,
        data: blogs,
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      };

      res.json(response);
    } catch (error) {
      console.error('Error fetching blogs:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to fetch blogs'
      };
      res.status(500).json(response);
    }
  },

  // GET /api/blogs/:id
  getBlogById: async (req: Request, res: Response): Promise<void> => {
    try {
      const id = parseInt(req.params.id);
      const blogRepository = getBlogRepository();

      const blog = await blogRepository.findOne({
        where: { id, is_published: true },
        relations: ['author', 'comments', 'comments.sender']
      });

      if (!blog) {
        const response: ApiResponse = {
          success: false,
          error: 'Blog not found'
        };
        res.status(404).json(response);
        return;
      }

      // Increment view count
      await blogRepository.update(id, { view_count: blog.view_count + 1 });

      const response: ApiResponse<Blog> = {
        success: true,
        data: blog
      };

      res.json(response);
    } catch (error) {
      console.error('Error fetching blog:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to fetch blog'
      };
      res.status(500).json(response);
    }
  },

  // POST /api/blogs
  createBlog: async (req: Request, res: Response): Promise<void> => {
    try {
      const blogData: CreateBlogRequest = req.body;
      const userId = req.userId;

      if (!userId) {
        const response: ApiResponse = {
          success: false,
          error: 'Authentication required'
        };
        res.status(401).json(response);
        return;
      }

      const blogRepository = getBlogRepository();

      const newBlog = blogRepository.create({
        topic: blogData.topic,
        subject: blogData.subject,
        content: blogData.content,
        language: blogData.language,
        author_id: userId,
        is_published: true,
        view_count: 0
      });

      const savedBlog = await blogRepository.save(newBlog);

      const response: ApiResponse<Blog> = {
        success: true,
        data: savedBlog,
        message: 'Blog created successfully'
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('Error creating blog:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to create blog'
      };
      res.status(500).json(response);
    }
  },

  // PUT /api/blogs/:id
  updateBlog: async (req: Request, res: Response): Promise<void> => {
    try {
      const id = parseInt(req.params.id);
      const updateData: UpdateBlogRequest = req.body;
      const userId = req.userId;

      if (!userId) {
        const response: ApiResponse = {
          success: false,
          error: 'Authentication required'
        };
        res.status(401).json(response);
        return;
      }

      const blogRepository = getBlogRepository();

      const blog = await blogRepository.findOne({
        where: { id }
      });

      if (!blog) {
        const response: ApiResponse = {
          success: false,
          error: 'Blog not found'
        };
        res.status(404).json(response);
        return;
      }

      // Check if user is the author or admin
      if (blog.author_id !== userId && !req.user?.is_admin) {
        const response: ApiResponse = {
          success: false,
          error: 'You can only edit your own blogs'
        };
        res.status(403).json(response);
        return;
      }

      await blogRepository.update(id, updateData);

      const updatedBlog = await blogRepository.findOne({
        where: { id },
        relations: ['author']
      });

      const response: ApiResponse<Blog> = {
        success: true,
        data: updatedBlog!,
        message: 'Blog updated successfully'
      };

      res.json(response);
    } catch (error) {
      console.error('Error updating blog:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to update blog'
      };
      res.status(500).json(response);
    }
  },

  // DELETE /api/blogs/:id
  deleteBlog: async (req: Request, res: Response): Promise<void> => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.userId;

      if (!userId) {
        const response: ApiResponse = {
          success: false,
          error: 'Authentication required'
        };
        res.status(401).json(response);
        return;
      }

      const blogRepository = getBlogRepository();

      const blog = await blogRepository.findOne({
        where: { id }
      });

      if (!blog) {
        const response: ApiResponse = {
          success: false,
          error: 'Blog not found'
        };
        res.status(404).json(response);
        return;
      }

      // Check if user is the author or admin
      if (blog.author_id !== userId && !req.user?.is_admin) {
        const response: ApiResponse = {
          success: false,
          error: 'You can only delete your own blogs'
        };
        res.status(403).json(response);
        return;
      }

      await blogRepository.delete(id);

      const response: ApiResponse = {
        success: true,
        message: 'Blog deleted successfully'
      };

      res.json(response);
    } catch (error) {
      console.error('Error deleting blog:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to delete blog'
      };
      res.status(500).json(response);
    }
  },

  // BLOG COMMENTS

  // GET /api/blogs/:blogId/comments
  getBlogComments: async (req: Request, res: Response): Promise<void> => {
    try {
      const blogId = parseInt(req.params.blogId);
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const skip = (page - 1) * limit;

      const blogCommentRepository = getBlogCommentRepository();

      const [comments, total] = await blogCommentRepository.findAndCount({
        where: { blog_id: blogId },
        relations: ['sender', 'receiver', 'parentComment', 'replies'],
        skip,
        take: limit,
        order: { created_at: 'ASC' }
      });

      const totalPages = Math.ceil(total / limit);

      const response: PaginatedResponse<BlogComment> = {
        success: true,
        data: comments,
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      };

      res.json(response);
    } catch (error) {
      console.error('Error fetching blog comments:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to fetch blog comments'
      };
      res.status(500).json(response);
    }
  },

  // POST /api/blogs/:blogId/comments
  createBlogComment: async (req: Request, res: Response): Promise<void> => {
    try {
      const blogId = parseInt(req.params.blogId);
      const commentData: CreateBlogCommentRequest = req.body;
      const userId = req.userId;

      if (!userId) {
        const response: ApiResponse = {
          success: false,
          error: 'Authentication required'
        };
        res.status(401).json(response);
        return;
      }

      const blogCommentRepository = getBlogCommentRepository();

      const newComment = blogCommentRepository.create({
        blog_id: blogId,
        sender_id: userId,
        receiver_id: commentData.receiver_id,
        parent_comment_id: commentData.parent_comment_id,
        content: commentData.content,
        message_type: commentData.message_type
      });

      const savedComment = await blogCommentRepository.save(newComment);

      const response: ApiResponse<BlogComment> = {
        success: true,
        data: savedComment,
        message: 'Comment created successfully'
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('Error creating blog comment:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to create comment'
      };
      res.status(500).json(response);
    }
  },

  // DELETE /api/blogs/:blogId/comments/:commentId
  deleteBlogComment: async (req: Request, res: Response): Promise<void> => {
    try {
      const commentId = parseInt(req.params.commentId);
      const userId = req.userId;

      if (!userId) {
        const response: ApiResponse = {
          success: false,
          error: 'Authentication required'
        };
        res.status(401).json(response);
        return;
      }

      const blogCommentRepository = getBlogCommentRepository();

      const comment = await blogCommentRepository.findOne({
        where: { id: commentId }
      });

      if (!comment) {
        const response: ApiResponse = {
          success: false,
          error: 'Comment not found'
        };
        res.status(404).json(response);
        return;
      }

      // Check if user is the author or admin
      if (comment.sender_id !== userId && !req.user?.is_admin) {
        const response: ApiResponse = {
          success: false,
          error: 'You can only delete your own comments'
        };
        res.status(403).json(response);
        return;
      }

      await blogCommentRepository.delete(commentId);

      const response: ApiResponse = {
        success: true,
        message: 'Comment deleted successfully'
      };

      res.json(response);
    } catch (error) {
      console.error('Error deleting blog comment:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to delete comment'
      };
      res.status(500).json(response);
    }
  },

  // GET /api/blogs/:blogId/comments/:commentId/reactions
  getBlogCommentReactions: async (req: Request, res: Response): Promise<void> => {
    try {
      const commentId = parseInt(req.params.commentId);

      const blogCommentReactionRepository = getBlogCommentReactionRepository();

      const reactions = await blogCommentReactionRepository.find({
        where: { blog_id: commentId },
        relations: ['sender']
      });

      const response: ApiResponse<BlogCommentReaction[]> = {
        success: true,
        data: reactions
      };

      res.json(response);
    } catch (error) {
      console.error('Error fetching blog comment reactions:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to fetch reactions'
      };
      res.status(500).json(response);
    }
  },

  // POST /api/blogs/:blogId/comments/:commentId/reactions
  createBlogCommentReaction: async (req: Request, res: Response): Promise<void> => {
    try {
      const commentId = parseInt(req.params.commentId);
      const reactionData: CreateBlogCommentReactionRequest = req.body;
      const userId = req.userId;

      if (!userId) {
        const response: ApiResponse = {
          success: false,
          error: 'Authentication required'
        };
        res.status(401).json(response);
        return;
      }

      const blogCommentReactionRepository = getBlogCommentReactionRepository();

      // Check if user already reacted to this comment
      const existingReaction = await blogCommentReactionRepository.findOne({
        where: { blog_: commentId, sender_id: userId }
      });

      if (existingReaction) {
        // Update existing reaction
        await blogCommentReactionRepository.update(existingReaction.id, {
          message_type: reactionData.message_type
        });

        const updatedReaction = await blogCommentReactionRepository.findOne({
          where: { id: existingReaction.id },
          relations: ['sender']
        });

        const response: ApiResponse<BlogCommentReaction> = {
          success: true,
          data: updatedReaction!,
          message: 'Reaction updated successfully'
        };

        res.json(response);
      } else {
        // Create new reaction
        const newReaction = blogCommentReactionRepository.create({
          blog_comment_id: commentId,
          sender_id: userId,
          message_type: reactionData.message_type
        });

        const savedReaction = await blogCommentReactionRepository.save(newReaction);

        const response: ApiResponse<BlogCommentReaction> = {
          success: true,
          data: savedReaction,
          message: 'Reaction created successfully'
        };

        res.status(201).json(response);
      }
    } catch (error) {
      console.error('Error creating blog comment reaction:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to create reaction'
      };
      res.status(500).json(response);
    }
  }
};
