import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { AppDataSource } from '../config/database';
import { UserEntity, ApiResponse } from '@canmoms/shared';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: UserEntity;
      userId?: number;
    }
  }
}

interface JwtPayload {
  userId: number;
  email: string;
  is_admin: boolean;
}

export const authenticateToken = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      const response: ApiResponse = {
        success: false,
        error: 'Access token required'
      };
      res.status(401).json(response);
      return;
    }

    const jwtSecret = process.env.JWT_SECRET || 'your-super-secret-jwt-key';
    const decoded = jwt.verify(token, jwtSecret) as JwtPayload;

    // Get user from database
    const userRepository = AppDataSource.getRepository(UserEntity);
    const user = await userRepository.findOne({
      where: { id: decoded.userId }
    });

    if (!user) {
      const response: ApiResponse = {
        success: false,
        error: 'User not found'
      };
      res.status(401).json(response);
      return;
    }

    // Check if user is banned
    if (user.is_banned) {
      const now = new Date();
      if (!user.banned_until || user.banned_until > now) {
        const response: ApiResponse = {
          success: false,
          error: 'User account is banned'
        };
        res.status(403).json(response);
        return;
      } else {
        // Unban user if ban period has expired
        await userRepository.update(user.id, {
          is_banned: false,
          banned_until: undefined
        });
        user.is_banned = false;
        user.banned_until = undefined;
      }
    }

    req.user = user;
    req.userId = user.id;
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Invalid or expired token'
    };
    res.status(401).json(response);
  }
};

export const requireAdmin = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.user || !req.user.is_admin) {
    const response: ApiResponse = {
      success: false,
      error: 'Admin access required'
    };
    res.status(403).json(response);
    return;
  }
  next();
};

export const optionalAuth = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      const jwtSecret = process.env.JWT_SECRET || 'your-super-secret-jwt-key';
      const decoded = jwt.verify(token, jwtSecret) as JwtPayload;

      const userRepository = AppDataSource.getRepository(UserEntity);
      const user = await userRepository.findOne({
        where: { id: decoded.userId }
      });

      if (user && !user.is_banned) {
        req.user = user;
        req.userId = user.id;
      }
    }

    next();
  } catch (error) {
    // Continue without authentication for optional auth
    next();
  }
};
