# Database Setup Guide

## Prerequisites

1. **Install PostgreSQL**
   - Download and install PostgreSQL from https://www.postgresql.org/download/
   - Make sure PostgreSQL service is running

2. **Create Database**
   ```sql
   -- Connect to PostgreSQL as superuser
   CREATE DATABASE canmoms;
   CREATE USER canmoms_user WITH PASSWORD 'your_password';
   GRANT ALL PRIVILEGES ON DATABASE canmoms TO canmoms_user;
   ```

## Setup Steps

### 1. Environment Configuration

Copy the `.env.example` file in the backend directory to `.env` and update the values:

```bash
cd apps/backend
cp .env.example .env
```

Update the database configuration in `.env`:
```env
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=canmoms_user
DB_PASSWORD=your_password
DB_NAME=canmoms
```

### 2. Create Database Schema

Run the table creation script:

```bash
# Connect to your PostgreSQL database
psql -h localhost -U canmoms_user -d canmoms

# Then run the SQL script
\i apps/shared/src/database/create-tables.sql
```

Or copy and paste the contents of `create-tables.sql` into your PostgreSQL client.

### 3. Start the Backend

```bash
cd apps/backend
npm run dev
```

The backend will automatically connect to the database using TypeORM.

## Database Schema Overview

### Core Tables:
- **users** - User accounts and profiles
- **groups** - Community groups
- **group_members** - Group membership relationships
- **group_messages** - Messages within groups
- **private_messages** - Direct messages between users
- **reports** - User reports for moderation
- **blogs** - Blog posts
- **blog_comments** - Comments on blog posts
- **blog_comment_reactions** - Reactions to blog comments

### Key Features:
- **ENUM types** for message types and languages
- **Foreign key constraints** for data integrity
- **Indexes** for query performance
- **Triggers** for automatic timestamp updates
- **JSONB** support for flexible user preferences

## Development Notes

- The backend uses TypeORM with entity synchronization in development mode
- In production, use migrations instead of synchronization
- All timestamps are stored with timezone information
- User passwords should be hashed before storage (implement bcrypt)
- Consider adding Redis for session management and caching

## Troubleshooting

### Connection Issues:
1. Verify PostgreSQL is running: `sudo systemctl status postgresql`
2. Check database exists: `psql -l`
3. Verify user permissions: `\du` in psql
4. Check firewall settings if connecting remotely

### Schema Issues:
1. Drop and recreate database if needed:
   ```sql
   DROP DATABASE canmoms;
   CREATE DATABASE canmoms;
   ```
2. Re-run the create-tables.sql script
3. Check for any missing ENUM types

### TypeORM Issues:
1. Ensure `synchronize: false` in production
2. Use migrations for schema changes in production
3. Check entity imports in the database configuration
