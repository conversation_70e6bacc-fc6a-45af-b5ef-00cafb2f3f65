import { Request, Response } from 'express';
import { Repository } from 'typeorm';
import { AppDataSource } from '../config/database';
import {
  GroupMessage,
  PrivateMessage,
  CreateGroupMessageRequest,
  CreatePrivateMessageRequest,
  ApiResponse,
  PaginatedResponse,
  GroupMessageEntity,
  PrivateMessageEntity,
  GroupMemberEntity
} from '@canmoms/shared';

// Get repository helpers
const getGroupMessageRepository = (): Repository<GroupMessageEntity> => {
  return AppDataSource.getRepository(GroupMessageEntity);
};

const getPrivateMessageRepository = (): Repository<PrivateMessageEntity> => {
  return AppDataSource.getRepository(PrivateMessageEntity);
};

const getGroupMemberRepository = (): Repository<GroupMemberEntity> => {
  return AppDataSource.getRepository(GroupMemberEntity);
};

export const messagingController = {
  // GROUP MESSAGES

  // GET /api/groups/:groupId/messages
  getGroupMessages: async (req: Request, res: Response): Promise<void> => {
    try {
      const groupId = parseInt(req.params.groupId);
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 50;
      const skip = (page - 1) * limit;
      const userId = req.userId;

      if (!userId) {
        const response: ApiResponse = {
          success: false,
          error: 'Authentication required'
        };
        res.status(401).json(response);
        return;
      }

      // Check if user is member of the group
      const groupMemberRepository = getGroupMemberRepository();
      const membership = await groupMemberRepository.findOne({
        where: { user_id: userId, group_id: groupId }
      });

      if (!membership) {
        const response: ApiResponse = {
          success: false,
          error: 'You are not a member of this group'
        };
        res.status(403).json(response);
        return;
      }

      const groupMessageRepository = getGroupMessageRepository();
      const [messages, total] = await groupMessageRepository.findAndCount({
        where: { group_id: groupId },
        relations: ['sender'],
        skip,
        take: limit,
        order: { created_at: 'DESC' }
      });

      const totalPages = Math.ceil(total / limit);

      const response: PaginatedResponse<GroupMessage> = {
        success: true,
        data: messages,
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      };

      res.json(response);
    } catch (error) {
      console.error('Error fetching group messages:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to fetch group messages'
      };
      res.status(500).json(response);
    }
  },

  // POST /api/groups/:groupId/messages
  createGroupMessage: async (req: Request, res: Response): Promise<void> => {
    try {
      const groupId = parseInt(req.params.groupId);
      const messageData: CreateGroupMessageRequest = req.body;
      const senderId = parseInt(req.headers['user-id'] as string); // Assuming user ID from auth middleware

      const newMessage: GroupMessage = {
        id: nextGroupMessageId++,
        group_id: groupId,
        sender_id: senderId,
        reacted_message_id: messageData.reacted_message_id,
        message_type: messageData.message_type,
        content: messageData.content,
        created_at: new Date()
      };

      groupMessages.push(newMessage);

      const response: ApiResponse<GroupMessage> = {
        success: true,
        data: newMessage,
        message: 'Group message sent successfully'
      };

      res.status(201).json(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: 'Failed to send group message'
      };
      res.status(500).json(response);
    }
  },

  // GET /api/groups/:groupId/messages/:messageId
  getGroupMessageById: async (req: Request, res: Response): Promise<void> => {
    try {
      const groupId = parseInt(req.params.groupId);
      const messageId = parseInt(req.params.messageId);

      const message = groupMessages.find(gm => gm.id === messageId && gm.group_id === groupId);

      if (!message) {
        const response: ApiResponse = {
          success: false,
          error: 'Group message not found'
        };
        res.status(404).json(response);
        return;
      }

      const response: ApiResponse<GroupMessage> = {
        success: true,
        data: message
      };

      res.json(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: 'Failed to fetch group message'
      };
      res.status(500).json(response);
    }
  },

  // DELETE /api/groups/:groupId/messages/:messageId
  deleteGroupMessage: async (req: Request, res: Response): Promise<void> => {
    try {
      const groupId = parseInt(req.params.groupId);
      const messageId = parseInt(req.params.messageId);
      const userId = parseInt(req.headers['user-id'] as string);

      const messageIndex = groupMessages.findIndex(gm => 
        gm.id === messageId && 
        gm.group_id === groupId && 
        gm.sender_id === userId
      );

      if (messageIndex === -1) {
        const response: ApiResponse = {
          success: false,
          error: 'Group message not found or unauthorized'
        };
        res.status(404).json(response);
        return;
      }

      groupMessages.splice(messageIndex, 1);

      const response: ApiResponse = {
        success: true,
        message: 'Group message deleted successfully'
      };

      res.json(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: 'Failed to delete group message'
      };
      res.status(500).json(response);
    }
  },

  // PRIVATE MESSAGES

  // GET /api/private-messages
  getPrivateMessages: async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = parseInt(req.headers['user-id'] as string);
      const otherUserId = parseInt(req.query.otherUserId as string);
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 50;

      let messages = privateMessages.filter(pm => 
        (pm.sender_id === userId && pm.receiver_id === otherUserId) ||
        (pm.sender_id === otherUserId && pm.receiver_id === userId)
      );

      // Sort by created_at descending (newest first)
      messages.sort((a, b) => b.created_at.getTime() - a.created_at.getTime());

      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedMessages = messages.slice(startIndex, endIndex);
      const totalPages = Math.ceil(messages.length / limit);

      const response: PaginatedResponse<PrivateMessage> = {
        success: true,
        data: paginatedMessages,
        pagination: {
          page,
          limit,
          total: messages.length,
          totalPages
        }
      };

      res.json(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: 'Failed to fetch private messages'
      };
      res.status(500).json(response);
    }
  },

  // POST /api/private-messages
  createPrivateMessage: async (req: Request, res: Response): Promise<void> => {
    try {
      const messageData: CreatePrivateMessageRequest = req.body;
      const senderId = parseInt(req.headers['user-id'] as string);

      const newMessage: PrivateMessage = {
        id: nextPrivateMessageId++,
        sender_id: senderId,
        receiver_id: messageData.receiver_id,
        message_type: messageData.message_type,
        content: messageData.content,
        created_at: new Date()
      };

      privateMessages.push(newMessage);

      const response: ApiResponse<PrivateMessage> = {
        success: true,
        data: newMessage,
        message: 'Private message sent successfully'
      };

      res.status(201).json(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: 'Failed to send private message'
      };
      res.status(500).json(response);
    }
  },

  // GET /api/private-messages/:messageId
  getPrivateMessageById: async (req: Request, res: Response): Promise<void> => {
    try {
      const messageId = parseInt(req.params.messageId);
      const userId = parseInt(req.headers['user-id'] as string);

      const message = privateMessages.find(pm => 
        pm.id === messageId && 
        (pm.sender_id === userId || pm.receiver_id === userId)
      );

      if (!message) {
        const response: ApiResponse = {
          success: false,
          error: 'Private message not found or unauthorized'
        };
        res.status(404).json(response);
        return;
      }

      const response: ApiResponse<PrivateMessage> = {
        success: true,
        data: message
      };

      res.json(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: 'Failed to fetch private message'
      };
      res.status(500).json(response);
    }
  },

  // DELETE /api/private-messages/:messageId
  deletePrivateMessage: async (req: Request, res: Response): Promise<void> => {
    try {
      const messageId = parseInt(req.params.messageId);
      const userId = parseInt(req.headers['user-id'] as string);

      const messageIndex = privateMessages.findIndex(pm => 
        pm.id === messageId && pm.sender_id === userId
      );

      if (messageIndex === -1) {
        const response: ApiResponse = {
          success: false,
          error: 'Private message not found or unauthorized'
        };
        res.status(404).json(response);
        return;
      }

      privateMessages.splice(messageIndex, 1);

      const response: ApiResponse = {
        success: true,
        message: 'Private message deleted successfully'
      };

      res.json(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: 'Failed to delete private message'
      };
      res.status(500).json(response);
    }
  },

  // GET /api/private-messages/conversations
  getConversations: async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = parseInt(req.headers['user-id'] as string);

      // Get unique conversations (other users this user has messaged with)
      const conversations = new Map<number, PrivateMessage>();

      privateMessages
        .filter(pm => pm.sender_id === userId || pm.receiver_id === userId)
        .sort((a, b) => b.created_at.getTime() - a.created_at.getTime())
        .forEach(message => {
          const otherUserId = message.sender_id === userId ? message.receiver_id : message.sender_id;
          if (!conversations.has(otherUserId)) {
            conversations.set(otherUserId, message);
          }
        });

      const response: ApiResponse<PrivateMessage[]> = {
        success: true,
        data: Array.from(conversations.values())
      };

      res.json(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: 'Failed to fetch conversations'
      };
      res.status(500).json(response);
    }
  }
};
