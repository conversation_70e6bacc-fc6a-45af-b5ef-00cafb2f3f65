import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import {
  Blog,
  BlogComment,
  BlogCommentReaction,
  CreateBlogRequest,
  UpdateBlogRequest,
  CreateBlogCommentRequest,
  CreateBlogCommentReactionRequest,
  ApiResponse,
  PaginatedResponse,
  Language,
  MessageType
} from '@canmoms/shared';

@Injectable({
  providedIn: 'root'
})
export class BlogService {
  private readonly baseUrl = 'http://localhost:3000/api/blogs';

  constructor(private http: HttpClient) {}

  // BLOG POSTS

  /**
   * Get all blogs with pagination and filters
   */
  getAllBlogs(
    page: number = 1, 
    limit: number = 10,
    filters?: {
      topic?: string;
      language?: Language;
    }
  ): Observable<PaginatedResponse<Blog>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    if (filters) {
      if (filters.topic) params = params.set('topic', filters.topic);
      if (filters.language) params = params.set('language', filters.language);
    }

    return this.http.get<PaginatedResponse<Blog>>(this.baseUrl, { params });
  }

  /**
   * Get blog by ID
   */
  getBlogById(id: number): Observable<ApiResponse<Blog>> {
    return this.http.get<ApiResponse<Blog>>(`${this.baseUrl}/${id}`);
  }

  /**
   * Create a new blog post
   */
  createBlog(blogData: CreateBlogRequest): Observable<ApiResponse<Blog>> {
    return this.http.post<ApiResponse<Blog>>(this.baseUrl, blogData);
  }

  /**
   * Update an existing blog post
   */
  updateBlog(id: number, blogData: UpdateBlogRequest): Observable<ApiResponse<Blog>> {
    return this.http.put<ApiResponse<Blog>>(`${this.baseUrl}/${id}`, blogData);
  }

  /**
   * Delete a blog post
   */
  deleteBlog(id: number): Observable<ApiResponse> {
    return this.http.delete<ApiResponse>(`${this.baseUrl}/${id}`);
  }

  /**
   * Get blogs by topic
   */
  getBlogsByTopic(topic: string, page: number = 1, limit: number = 10): Observable<PaginatedResponse<Blog>> {
    const params = new HttpParams()
      .set('topic', topic)
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get<PaginatedResponse<Blog>>(this.baseUrl, { params });
  }

  /**
   * Get blogs by language
   */
  getBlogsByLanguage(language: Language, page: number = 1, limit: number = 10): Observable<PaginatedResponse<Blog>> {
    const params = new HttpParams()
      .set('language', language)
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get<PaginatedResponse<Blog>>(this.baseUrl, { params });
  }

  /**
   * Search blogs
   */
  searchBlogs(query: string, page: number = 1, limit: number = 10): Observable<PaginatedResponse<Blog>> {
    const params = new HttpParams()
      .set('search', query)
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get<PaginatedResponse<Blog>>(this.baseUrl, { params });
  }

  // BLOG COMMENTS

  /**
   * Get comments for a blog post
   */
  getBlogComments(blogId: number, page: number = 1, limit: number = 20): Observable<PaginatedResponse<BlogComment>> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get<PaginatedResponse<BlogComment>>(`${this.baseUrl}/${blogId}/comments`, { params });
  }

  /**
   * Create a comment on a blog post
   */
  createBlogComment(blogId: number, commentData: Omit<CreateBlogCommentRequest, 'blog_id'>): Observable<ApiResponse<BlogComment>> {
    const payload: CreateBlogCommentRequest = {
      ...commentData,
      blog_id: blogId
    };
    return this.http.post<ApiResponse<BlogComment>>(`${this.baseUrl}/${blogId}/comments`, payload);
  }

  /**
   * Delete a blog comment
   */
  deleteBlogComment(blogId: number, commentId: number): Observable<ApiResponse> {
    return this.http.delete<ApiResponse>(`${this.baseUrl}/${blogId}/comments/${commentId}`);
  }

  /**
   * Reply to a blog comment
   */
  replyToBlogComment(
    blogId: number, 
    parentCommentId: number, 
    receiverId: number, 
    content: string
  ): Observable<ApiResponse<BlogComment>> {
    return this.createBlogComment(blogId, {
      parent_comment_id: parentCommentId,
      receiver_id: receiverId,
      message_type: MessageType.REPLY,
      content
    });
  }

  /**
   * Like a blog comment
   */
  likeBlogComment(blogId: number, receiverId: number, content: string = ''): Observable<ApiResponse<BlogComment>> {
    return this.createBlogComment(blogId, {
      receiver_id: receiverId,
      message_type: MessageType.LIKE,
      content
    });
  }

  /**
   * Dislike a blog comment
   */
  dislikeBlogComment(blogId: number, receiverId: number, content: string = ''): Observable<ApiResponse<BlogComment>> {
    return this.createBlogComment(blogId, {
      receiver_id: receiverId,
      message_type: MessageType.DISLIKE,
      content
    });
  }

  // BLOG COMMENT REACTIONS

  /**
   * Get reactions for a blog comment
   */
  getBlogCommentReactions(blogId: number, commentId: number): Observable<ApiResponse<BlogCommentReaction[]>> {
    return this.http.get<ApiResponse<BlogCommentReaction[]>>(`${this.baseUrl}/${blogId}/comments/${commentId}/reactions`);
  }

  /**
   * Create a reaction to a blog comment
   */
  createBlogCommentReaction(
    blogId: number, 
    commentId: number, 
    reactionData: Omit<CreateBlogCommentReactionRequest, 'blog_id' | 'commented_on'>
  ): Observable<ApiResponse<BlogCommentReaction>> {
    const payload: CreateBlogCommentReactionRequest = {
      ...reactionData,
      blog_id: blogId,
      commented_on: commentId
    };
    return this.http.post<ApiResponse<BlogCommentReaction>>(`${this.baseUrl}/${blogId}/comments/${commentId}/reactions`, payload);
  }

  /**
   * Like a blog comment reaction
   */
  likeBlogCommentReaction(
    blogId: number, 
    commentId: number, 
    receiverId: number, 
    content: string = ''
  ): Observable<ApiResponse<BlogCommentReaction>> {
    return this.createBlogCommentReaction(blogId, commentId, {
      receiver_id: receiverId,
      message_type: MessageType.LIKE,
      content
    });
  }

  /**
   * Dislike a blog comment reaction
   */
  dislikeBlogCommentReaction(
    blogId: number, 
    commentId: number, 
    receiverId: number, 
    content: string = ''
  ): Observable<ApiResponse<BlogCommentReaction>> {
    return this.createBlogCommentReaction(blogId, commentId, {
      receiver_id: receiverId,
      message_type: MessageType.DISLIKE,
      content
    });
  }

  /**
   * Reply to a blog comment reaction
   */
  replyToBlogCommentReaction(
    blogId: number, 
    commentId: number, 
    receiverId: number, 
    content: string
  ): Observable<ApiResponse<BlogCommentReaction>> {
    return this.createBlogCommentReaction(blogId, commentId, {
      receiver_id: receiverId,
      message_type: MessageType.REPLY,
      content
    });
  }

  // UTILITY METHODS

  /**
   * Get trending blogs
   */
  getTrendingBlogs(page: number = 1, limit: number = 10): Observable<PaginatedResponse<Blog>> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get<PaginatedResponse<Blog>>(`${this.baseUrl}/trending`, { params });
  }

  /**
   * Get recent blogs
   */
  getRecentBlogs(page: number = 1, limit: number = 10): Observable<PaginatedResponse<Blog>> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get<PaginatedResponse<Blog>>(`${this.baseUrl}/recent`, { params });
  }

  /**
   * Get blog topics
   */
  getBlogTopics(): Observable<ApiResponse<string[]>> {
    return this.http.get<ApiResponse<string[]>>(`${this.baseUrl}/topics`);
  }

  /**
   * Get blogs with most comments
   */
  getMostCommentedBlogs(page: number = 1, limit: number = 10): Observable<PaginatedResponse<Blog>> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get<PaginatedResponse<Blog>>(`${this.baseUrl}/most-commented`, { params });
  }
}
