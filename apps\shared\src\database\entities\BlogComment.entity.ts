import 'reflect-metadata';
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne, JoinColumn, OneToMany } from 'typeorm';
import { MessageType } from '../../types';
import { User } from './User.entity';
import { Blog } from './Blog.entity';
import { BlogCommentReaction } from './BlogCommentReaction.entity';

@Entity('blog_comments')
export class BlogComment {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  blog_id: number;

  @Column({ nullable: true })
  parent_comment_id?: number;

  @Column()
  sender_id: number;

  @Column()
  receiver_id: number;

  @Column({
    type: 'enum',
    enum: MessageType
  })
  message_type: MessageType;

  @Column({ type: 'text' })
  content: string;

  @CreateDateColumn({ type: 'timestamp with time zone' })
  created_at: Date;

  // Relations
  @ManyToOne(() => Blog, blog => blog.comments, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'blog_id' })
  blog: Blog;

  @ManyToOne(() => BlogComment, { onDelete: 'CASCADE', nullable: true })
  @JoinColumn({ name: 'parent_comment_id' })
  parentComment?: BlogComment;

  @OneToMany(() => BlogComment, blogComment => blogComment.parentComment)
  replies: BlogComment[];

  @ManyToOne(() => User, user => user.blogComments, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'sender_id' })
  sender: User;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'receiver_id' })
  receiver: User;

  @OneToMany(() => BlogCommentReaction, reaction => reaction.comment)
  reactions: BlogCommentReaction[];
}
