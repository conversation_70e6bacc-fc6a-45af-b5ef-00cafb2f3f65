import { Request, Response } from 'express';
import { Repository } from 'typeorm';
import { AppDataSource } from '../config/database';
import {
  Group,
  GroupMember,
  CreateGroupRequest,
  UpdateGroupRequest,
  ApiResponse,
  PaginatedResponse,
  GroupEntity,
  GroupMemberEntity
} from '@canmoms/shared';

// Get repository helpers
const getGroupRepository = (): Repository<GroupEntity> => {
  return AppDataSource.getRepository(GroupEntity);
};

const getGroupMemberRepository = (): Repository<GroupMemberEntity> => {
  return AppDataSource.getRepository(GroupMemberEntity);
};

export const groupController = {
  // GET /api/groups
  getAllGroups: async (req: Request, res: Response): Promise<void> => {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const skip = (page - 1) * limit;

      const groupRepository = getGroupRepository();
      const queryBuilder = groupRepository.createQueryBuilder('group');

      // Add filters
      if (req.query.country) {
        queryBuilder.andWhere('group.country = :country', { country: req.query.country });
      }
      if (req.query.city) {
        queryBuilder.andWhere('group.city = :city', { city: req.query.city });
      }
      if (req.query.language) {
        queryBuilder.andWhere('group.language = :language', { language: req.query.language });
      }
      if (req.query.topic) {
        queryBuilder.andWhere('group.topic ILIKE :topic', { topic: `%${req.query.topic}%` });
      }
      if (req.query.isPublic !== undefined) {
        queryBuilder.andWhere('group.is_public = :isPublic', { isPublic: req.query.isPublic === 'true' });
      }

      const [groups, total] = await queryBuilder
        .skip(skip)
        .take(limit)
        .orderBy('group.created_at', 'DESC')
        .getManyAndCount();

      const totalPages = Math.ceil(total / limit);

      const response: PaginatedResponse<Group> = {
        success: true,
        data: groups,
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      };

      res.json(response);
    } catch (error) {
      console.error('Error fetching groups:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to fetch groups'
      };
      res.status(500).json(response);
    }
  },

  // GET /api/groups/:id
  getGroupById: async (req: Request, res: Response): Promise<void> => {
    try {
      const id = parseInt(req.params.id);
      const groupRepository = getGroupRepository();

      const group = await groupRepository.findOne({
        where: { id },
        relations: ['members', 'members.user']
      });

      if (!group) {
        const response: ApiResponse = {
          success: false,
          error: 'Group not found'
        };
        res.status(404).json(response);
        return;
      }

      const response: ApiResponse<Group> = {
        success: true,
        data: group
      };

      res.json(response);
    } catch (error) {
      console.error('Error fetching group:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to fetch group'
      };
      res.status(500).json(response);
    }
  },

  // POST /api/groups
  createGroup: async (req: Request, res: Response): Promise<void> => {
    try {
      const groupData: CreateGroupRequest = req.body;
      const userId = req.userId;

      if (!userId) {
        const response: ApiResponse = {
          success: false,
          error: 'Authentication required'
        };
        res.status(401).json(response);
        return;
      }

      const groupRepository = getGroupRepository();
      const groupMemberRepository = getGroupMemberRepository();

      // Create new group
      const newGroup = groupRepository.create({
        region: groupData.region,
        city: groupData.city,
        country: groupData.country,
        birth_year: groupData.birth_year,
        language: groupData.language,
        topic: groupData.topic,
        is_public: groupData.is_public
      });

      const savedGroup = await groupRepository.save(newGroup);

      // Add creator as moderator
      const groupMember = groupMemberRepository.create({
        user_id: userId,
        group_id: savedGroup.id,
        is_moderator: true
      });

      await groupMemberRepository.save(groupMember);

      const response: ApiResponse<Group> = {
        success: true,
        data: savedGroup,
        message: 'Group created successfully'
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('Error creating group:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to create group'
      };
      res.status(500).json(response);
    }
  },

  // PUT /api/groups/:id
  updateGroup: async (req: Request, res: Response): Promise<void> => {
    try {
      const id = parseInt(req.params.id);
      const updateData: UpdateGroupRequest = req.body;

      const groupIndex = groups.findIndex(g => g.id === id);
      if (groupIndex === -1) {
        const response: ApiResponse = {
          success: false,
          error: 'Group not found'
        };
        res.status(404).json(response);
        return;
      }

      groups[groupIndex] = {
        ...groups[groupIndex],
        ...updateData
      };

      const response: ApiResponse<Group> = {
        success: true,
        data: groups[groupIndex],
        message: 'Group updated successfully'
      };

      res.json(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: 'Failed to update group'
      };
      res.status(500).json(response);
    }
  },

  // DELETE /api/groups/:id
  deleteGroup: async (req: Request, res: Response): Promise<void> => {
    try {
      const id = parseInt(req.params.id);
      const groupIndex = groups.findIndex(g => g.id === id);

      if (groupIndex === -1) {
        const response: ApiResponse = {
          success: false,
          error: 'Group not found'
        };
        res.status(404).json(response);
        return;
      }

      groups.splice(groupIndex, 1);
      // Also remove all group members
      groupMembers = groupMembers.filter(gm => gm.group_id !== id);

      const response: ApiResponse = {
        success: true,
        message: 'Group deleted successfully'
      };

      res.json(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: 'Failed to delete group'
      };
      res.status(500).json(response);
    }
  },

  // GET /api/groups/:id/members
  getGroupMembers: async (req: Request, res: Response): Promise<void> => {
    try {
      const groupId = parseInt(req.params.id);
      const members = groupMembers.filter(gm => gm.group_id === groupId);

      const response: ApiResponse<GroupMember[]> = {
        success: true,
        data: members
      };

      res.json(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: 'Failed to fetch group members'
      };
      res.status(500).json(response);
    }
  },

  // POST /api/groups/:id/members
  addGroupMember: async (req: Request, res: Response): Promise<void> => {
    try {
      const groupId = parseInt(req.params.id);
      const { user_id, is_moderator = false } = req.body;

      // Check if group exists
      const group = groups.find(g => g.id === groupId);
      if (!group) {
        const response: ApiResponse = {
          success: false,
          error: 'Group not found'
        };
        res.status(404).json(response);
        return;
      }

      // Check if user is already a member
      const existingMember = groupMembers.find(gm => gm.group_id === groupId && gm.user_id === user_id);
      if (existingMember) {
        const response: ApiResponse = {
          success: false,
          error: 'User is already a member of this group'
        };
        res.status(400).json(response);
        return;
      }

      const newMember: GroupMember = {
        id: nextGroupMemberId++,
        user_id,
        group_id: groupId,
        joined_at: new Date(),
        is_moderator
      };

      groupMembers.push(newMember);

      const response: ApiResponse<GroupMember> = {
        success: true,
        data: newMember,
        message: 'Member added to group successfully'
      };

      res.status(201).json(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: 'Failed to add member to group'
      };
      res.status(500).json(response);
    }
  },

  // DELETE /api/groups/:id/members/:memberId
  removeGroupMember: async (req: Request, res: Response): Promise<void> => {
    try {
      const groupId = parseInt(req.params.id);
      const memberId = parseInt(req.params.memberId);

      const memberIndex = groupMembers.findIndex(gm => gm.id === memberId && gm.group_id === groupId);
      if (memberIndex === -1) {
        const response: ApiResponse = {
          success: false,
          error: 'Group member not found'
        };
        res.status(404).json(response);
        return;
      }

      groupMembers.splice(memberIndex, 1);

      const response: ApiResponse = {
        success: true,
        message: 'Member removed from group successfully'
      };

      res.json(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: 'Failed to remove member from group'
      };
      res.status(500).json(response);
    }
  },

  // PUT /api/groups/:id/members/:memberId/moderator
  updateMemberModeratorStatus: async (req: Request, res: Response): Promise<void> => {
    try {
      const groupId = parseInt(req.params.id);
      const memberId = parseInt(req.params.memberId);
      const { is_moderator } = req.body;

      const memberIndex = groupMembers.findIndex(gm => gm.id === memberId && gm.group_id === groupId);
      if (memberIndex === -1) {
        const response: ApiResponse = {
          success: false,
          error: 'Group member not found'
        };
        res.status(404).json(response);
        return;
      }

      groupMembers[memberIndex].is_moderator = is_moderator;

      const response: ApiResponse<GroupMember> = {
        success: true,
        data: groupMembers[memberIndex],
        message: 'Member moderator status updated successfully'
      };

      res.json(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: 'Failed to update member moderator status'
      };
      res.status(500).json(response);
    }
  }
};
