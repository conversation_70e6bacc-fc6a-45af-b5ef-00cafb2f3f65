{"name": "canmoms-backend", "version": "1.0.0", "main": "index.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "lint": "eslint 'src/**/*.ts'", "test": "jest", "test:watch": "jest --watch", "db:setup": "psql -h localhost -U postgres -d canmoms -f ../shared/src/database/create-tables.sql", "db:reset": "npm run db:drop && npm run db:create && npm run db:setup", "db:create": "createdb -h localhost -U postgres canmoms", "db:drop": "dropdb -h localhost -U postgres canmoms --if-exists"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@canmoms/shared": "^1.0.0", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/jsonwebtoken": "^9.0.10", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^24.0.14", "eslint": "^9.31.0", "jest": "^30.0.4", "nodemon": "^3.1.10", "prettier": "^3.6.2", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}