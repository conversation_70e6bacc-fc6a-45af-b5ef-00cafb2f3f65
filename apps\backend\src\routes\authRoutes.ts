import { Router } from 'express';
import { authController } from '../controllers/authController';
import { authenticateToken } from '../middlewares/auth.middleware';

const router = Router();

// Public routes
router.post('/register', authController.register);
router.post('/login', authController.login);

// Protected routes
router.post('/verify', authenticateToken, authController.verifyAccount);
router.post('/request-otp', authenticateToken, authController.requestOTP);
router.get('/me', authenticateToken, authController.getCurrentUser);

export default router;
