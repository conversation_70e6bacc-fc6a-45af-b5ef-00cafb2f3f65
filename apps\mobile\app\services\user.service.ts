import { Http } from '@nativescript/core';
import { Observable, BehaviorSubject } from 'rxjs';
import {
  User,
  CreateUserRequest,
  UpdateUserRequest,
  ApiResponse,
  PaginatedResponse
} from '@canmoms/shared';

export class UserService {
  private readonly baseUrl = 'http://localhost:3000/api/users';
  private currentUser$ = new BehaviorSubject<User | null>(null);

  /**
   * Get current user observable
   */
  getCurrentUser(): Observable<User | null> {
    return this.currentUser$.asObservable();
  }

  /**
   * Set current user
   */
  setCurrentUser(user: User | null): void {
    this.currentUser$.next(user);
  }

  /**
   * Get all users with pagination
   */
  async getAllUsers(page: number = 1, limit: number = 10): Promise<PaginatedResponse<User>> {
    const url = `${this.baseUrl}?page=${page}&limit=${limit}`;
    const response = await Http.getJSON<PaginatedResponse<User>>(url);
    return response;
  }

  /**
   * Get user by ID
   */
  async getUserById(id: number): Promise<ApiResponse<User>> {
    const url = `${this.baseUrl}/${id}`;
    const response = await Http.getJSON<ApiResponse<User>>(url);
    return response;
  }

  /**
   * Create a new user
   */
  async createUser(userData: CreateUserRequest): Promise<ApiResponse<User>> {
    const response = await Http.request({
      url: this.baseUrl,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      content: JSON.stringify(userData)
    });

    return response.content.toJSON() as ApiResponse<User>;
  }

  /**
   * Update an existing user
   */
  async updateUser(id: number, userData: UpdateUserRequest): Promise<ApiResponse<User>> {
    const response = await Http.request({
      url: `${this.baseUrl}/${id}`,
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      content: JSON.stringify(userData)
    });

    return response.content.toJSON() as ApiResponse<User>;
  }

  /**
   * Delete a user
   */
  async deleteUser(id: number): Promise<ApiResponse> {
    const response = await Http.request({
      url: `${this.baseUrl}/${id}`,
      method: 'DELETE'
    });

    return response.content.toJSON() as ApiResponse;
  }

  /**
   * Ban a user
   */
  async banUser(id: number, bannedUntil?: Date): Promise<ApiResponse<User>> {
    const body = bannedUntil ? { banned_until: bannedUntil.toISOString() } : {};
    
    const response = await Http.request({
      url: `${this.baseUrl}/${id}/ban`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      content: JSON.stringify(body)
    });

    return response.content.toJSON() as ApiResponse<User>;
  }

  /**
   * Unban a user
   */
  async unbanUser(id: number): Promise<ApiResponse<User>> {
    const response = await Http.request({
      url: `${this.baseUrl}/${id}/unban`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      content: JSON.stringify({})
    });

    return response.content.toJSON() as ApiResponse<User>;
  }

  /**
   * Search users by username or email
   */
  async searchUsers(query: string, page: number = 1, limit: number = 10): Promise<PaginatedResponse<User>> {
    const url = `${this.baseUrl}?search=${encodeURIComponent(query)}&page=${page}&limit=${limit}`;
    const response = await Http.getJSON<PaginatedResponse<User>>(url);
    return response;
  }

  /**
   * Get user profile (current user)
   */
  async getCurrentUserProfile(): Promise<ApiResponse<User>> {
    const url = `${this.baseUrl}/profile`;
    const response = await Http.getJSON<ApiResponse<User>>(url);
    
    if (response.success && response.data) {
      this.setCurrentUser(response.data);
    }
    
    return response;
  }

  /**
   * Update current user profile
   */
  async updateCurrentUserProfile(userData: UpdateUserRequest): Promise<ApiResponse<User>> {
    const response = await Http.request({
      url: `${this.baseUrl}/profile`,
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      content: JSON.stringify(userData)
    });

    const result = response.content.toJSON() as ApiResponse<User>;
    
    if (result.success && result.data) {
      this.setCurrentUser(result.data);
    }
    
    return result;
  }

  /**
   * Verify user account with OTP
   */
  async verifyAccount(otpCode: number): Promise<ApiResponse<User>> {
    const response = await Http.request({
      url: `${this.baseUrl}/verify`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      content: JSON.stringify({ otp_code: otpCode })
    });

    const result = response.content.toJSON() as ApiResponse<User>;
    
    if (result.success && result.data) {
      this.setCurrentUser(result.data);
    }
    
    return result;
  }

  /**
   * Request OTP for account verification
   */
  async requestOTP(): Promise<ApiResponse> {
    const response = await Http.request({
      url: `${this.baseUrl}/request-otp`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      content: JSON.stringify({})
    });

    return response.content.toJSON() as ApiResponse;
  }

  /**
   * Change user password
   */
  async changePassword(currentPassword: string, newPassword: string): Promise<ApiResponse> {
    const response = await Http.request({
      url: `${this.baseUrl}/change-password`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      content: JSON.stringify({
        current_password: currentPassword,
        new_password: newPassword
      })
    });

    return response.content.toJSON() as ApiResponse;
  }

  /**
   * Get users by country/city for location-based features
   */
  async getUsersByLocation(country?: string, city?: string, page: number = 1, limit: number = 10): Promise<PaginatedResponse<User>> {
    let url = `${this.baseUrl}/location?page=${page}&limit=${limit}`;
    
    if (country) {
      url += `&country=${encodeURIComponent(country)}`;
    }
    
    if (city) {
      url += `&city=${encodeURIComponent(city)}`;
    }

    const response = await Http.getJSON<PaginatedResponse<User>>(url);
    return response;
  }

  /**
   * Upload profile photo
   */
  async uploadProfilePhoto(imagePath: string): Promise<ApiResponse<{ url: string }>> {
    // Note: In a real implementation, you would use the file upload functionality
    // This is a simplified version
    const response = await Http.request({
      url: `${this.baseUrl}/profile/photo`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      content: JSON.stringify({ image_path: imagePath })
    });

    return response.content.toJSON() as ApiResponse<{ url: string }>;
  }

  /**
   * Logout user
   */
  logout(): void {
    this.setCurrentUser(null);
  }
}
