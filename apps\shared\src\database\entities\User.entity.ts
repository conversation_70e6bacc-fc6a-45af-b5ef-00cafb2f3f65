import 'reflect-metadata';
import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { GroupMember } from './GroupMember.entity';
import { GroupMessage } from './GroupMessage.entity';
import { PrivateMessage } from './PrivateMessage.entity';
import { Report } from './Report.entity';
import { Blog } from './Blog.entity';
import { BlogComment } from './BlogComment.entity';
import { BlogCommentReaction } from './BlogCommentReaction.entity';

@Entity('users')
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true, length: 255 })
  email: string;

  @Column({ length: 50 })
  phone: string;

  @Column({ unique: true, length: 100 })
  username: string;

  @Column({ length: 255 })
  password: string; // hashed

  @Column({ type: 'text', nullable: true })
  bio?: string;

  @Column({ length: 500, nullable: true })
  profile_photo?: string;

  @Column({ length: 100, nullable: true })
  country?: string;

  @Column({ length: 100, nullable: true })
  city?: string;

  @Column({ type: 'jsonb', default: {} })
  user_preferences: Record<string, any>;

  @CreateDateColumn({ type: 'timestamp with time zone' })
  created_at: Date;

  @Column({ default: false })
  is_banned: boolean;

  @Column({ type: 'timestamp with time zone', nullable: true })
  banned_until?: Date;

  @Column({ default: false })
  is_admin: boolean;

  @Column({ default: false })
  is_verified: boolean;

  @Column({ type: 'integer', nullable: true })
  otp_code?: number;

  @Column({ type: 'timestamp with time zone', nullable: true })
  otp_expiretion_date?: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone' })
  updated_at: Date;

  // Relations
  @OneToMany(() => GroupMember, groupMember => groupMember.user)
  groupMemberships: GroupMember[];

  @OneToMany(() => GroupMessage, groupMessage => groupMessage.sender)
  groupMessages: GroupMessage[];

  @OneToMany(() => PrivateMessage, privateMessage => privateMessage.sender)
  sentPrivateMessages: PrivateMessage[];

  @OneToMany(() => PrivateMessage, privateMessage => privateMessage.receiver)
  receivedPrivateMessages: PrivateMessage[];

  @OneToMany(() => Report, report => report.reporter)
  reportsMade: Report[];

  @OneToMany(() => Report, report => report.reportedUser)
  reportsReceived: Report[];

  @OneToMany(() => Blog, blog => blog.author)
  blogs: Blog[];

  @OneToMany(() => BlogComment, blogComment => blogComment.sender)
  blogComments: BlogComment[];

  @OneToMany(() => BlogCommentReaction, reaction => reaction.sender)
  blogCommentReactions: BlogCommentReaction[];
}
