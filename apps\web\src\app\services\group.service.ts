import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import {
  Group,
  GroupMember,
  CreateGroupRequest,
  UpdateGroupRequest,
  ApiResponse,
  PaginatedResponse,
  Language
} from '@canmoms/shared';

@Injectable({
  providedIn: 'root'
})
export class GroupService {
  private readonly baseUrl = 'http://localhost:3000/api/groups';

  constructor(private http: HttpClient) {}

  /**
   * Get all groups with pagination and filters
   */
  getAllGroups(
    page: number = 1, 
    limit: number = 10,
    filters?: {
      country?: string;
      city?: string;
      language?: Language;
      topic?: string;
      isPublic?: boolean;
    }
  ): Observable<PaginatedResponse<Group>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    if (filters) {
      if (filters.country) params = params.set('country', filters.country);
      if (filters.city) params = params.set('city', filters.city);
      if (filters.language) params = params.set('language', filters.language);
      if (filters.topic) params = params.set('topic', filters.topic);
      if (filters.isPublic !== undefined) params = params.set('isPublic', filters.isPublic.toString());
    }

    return this.http.get<PaginatedResponse<Group>>(this.baseUrl, { params });
  }

  /**
   * Get group by ID
   */
  getGroupById(id: number): Observable<ApiResponse<Group>> {
    return this.http.get<ApiResponse<Group>>(`${this.baseUrl}/${id}`);
  }

  /**
   * Create a new group
   */
  createGroup(groupData: CreateGroupRequest): Observable<ApiResponse<Group>> {
    return this.http.post<ApiResponse<Group>>(this.baseUrl, groupData);
  }

  /**
   * Update an existing group
   */
  updateGroup(id: number, groupData: UpdateGroupRequest): Observable<ApiResponse<Group>> {
    return this.http.put<ApiResponse<Group>>(`${this.baseUrl}/${id}`, groupData);
  }

  /**
   * Delete a group
   */
  deleteGroup(id: number): Observable<ApiResponse> {
    return this.http.delete<ApiResponse>(`${this.baseUrl}/${id}`);
  }

  /**
   * Get group members
   */
  getGroupMembers(groupId: number): Observable<ApiResponse<GroupMember[]>> {
    return this.http.get<ApiResponse<GroupMember[]>>(`${this.baseUrl}/${groupId}/members`);
  }

  /**
   * Add member to group
   */
  addGroupMember(groupId: number, userId: number, isModerator: boolean = false): Observable<ApiResponse<GroupMember>> {
    return this.http.post<ApiResponse<GroupMember>>(`${this.baseUrl}/${groupId}/members`, {
      user_id: userId,
      is_moderator: isModerator
    });
  }

  /**
   * Remove member from group
   */
  removeGroupMember(groupId: number, memberId: number): Observable<ApiResponse> {
    return this.http.delete<ApiResponse>(`${this.baseUrl}/${groupId}/members/${memberId}`);
  }

  /**
   * Update member moderator status
   */
  updateMemberModeratorStatus(groupId: number, memberId: number, isModerator: boolean): Observable<ApiResponse<GroupMember>> {
    return this.http.put<ApiResponse<GroupMember>>(`${this.baseUrl}/${groupId}/members/${memberId}/moderator`, {
      is_moderator: isModerator
    });
  }

  /**
   * Join a group (current user)
   */
  joinGroup(groupId: number): Observable<ApiResponse<GroupMember>> {
    return this.http.post<ApiResponse<GroupMember>>(`${this.baseUrl}/${groupId}/join`, {});
  }

  /**
   * Leave a group (current user)
   */
  leaveGroup(groupId: number): Observable<ApiResponse> {
    return this.http.post<ApiResponse>(`${this.baseUrl}/${groupId}/leave`, {});
  }

  /**
   * Get groups by location
   */
  getGroupsByLocation(country: string, city?: string, page: number = 1, limit: number = 10): Observable<PaginatedResponse<Group>> {
    let params = new HttpParams()
      .set('country', country)
      .set('page', page.toString())
      .set('limit', limit.toString());

    if (city) {
      params = params.set('city', city);
    }

    return this.http.get<PaginatedResponse<Group>>(`${this.baseUrl}/location`, { params });
  }

  /**
   * Get groups by topic
   */
  getGroupsByTopic(topic: string, page: number = 1, limit: number = 10): Observable<PaginatedResponse<Group>> {
    const params = new HttpParams()
      .set('topic', topic)
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get<PaginatedResponse<Group>>(`${this.baseUrl}/topic`, { params });
  }

  /**
   * Get groups by language
   */
  getGroupsByLanguage(language: Language, page: number = 1, limit: number = 10): Observable<PaginatedResponse<Group>> {
    const params = new HttpParams()
      .set('language', language)
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get<PaginatedResponse<Group>>(`${this.baseUrl}/language`, { params });
  }

  /**
   * Search groups
   */
  searchGroups(query: string, page: number = 1, limit: number = 10): Observable<PaginatedResponse<Group>> {
    const params = new HttpParams()
      .set('search', query)
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get<PaginatedResponse<Group>>(this.baseUrl, { params });
  }

  /**
   * Get user's groups
   */
  getUserGroups(userId?: number, page: number = 1, limit: number = 10): Observable<PaginatedResponse<Group>> {
    let url = `${this.baseUrl}/user`;
    if (userId) {
      url += `/${userId}`;
    }

    const params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get<PaginatedResponse<Group>>(url, { params });
  }

  /**
   * Get recommended groups for user
   */
  getRecommendedGroups(page: number = 1, limit: number = 10): Observable<PaginatedResponse<Group>> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get<PaginatedResponse<Group>>(`${this.baseUrl}/recommended`, { params });
  }
}
