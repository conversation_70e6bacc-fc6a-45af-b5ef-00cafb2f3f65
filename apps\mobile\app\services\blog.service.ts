import { Http } from '@nativescript/core';
import {
  Blog,
  B<PERSON><PERSON>omment,
  BlogCommentReaction,
  CreateBlogRequest,
  UpdateBlogRequest,
  CreateBlogCommentRequest,
  CreateBlogCommentReactionRequest,
  ApiResponse,
  PaginatedResponse,
  Language,
  MessageType
} from '@canmoms/shared';

export class BlogService {
  private readonly baseUrl = 'http://localhost:3000/api/blogs';

  // BLOG POSTS

  /**
   * Get all blogs with pagination and filters
   */
  async getAllBlogs(
    page: number = 1, 
    limit: number = 10,
    filters?: {
      topic?: string;
      language?: Language;
    }
  ): Promise<PaginatedResponse<Blog>> {
    let url = `${this.baseUrl}?page=${page}&limit=${limit}`;

    if (filters) {
      if (filters.topic) url += `&topic=${encodeURIComponent(filters.topic)}`;
      if (filters.language) url += `&language=${filters.language}`;
    }

    const response = await Http.getJSON<PaginatedResponse<Blog>>(url);
    return response;
  }

  /**
   * Get blog by ID
   */
  async getBlogById(id: number): Promise<ApiResponse<Blog>> {
    const url = `${this.baseUrl}/${id}`;
    const response = await Http.getJSON<ApiResponse<Blog>>(url);
    return response;
  }

  /**
   * Create a new blog post
   */
  async createBlog(blogData: CreateBlogRequest): Promise<ApiResponse<Blog>> {
    const response = await Http.request({
      url: this.baseUrl,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      content: JSON.stringify(blogData)
    });

    return response.content.toJSON() as ApiResponse<Blog>;
  }

  /**
   * Update an existing blog post
   */
  async updateBlog(id: number, blogData: UpdateBlogRequest): Promise<ApiResponse<Blog>> {
    const response = await Http.request({
      url: `${this.baseUrl}/${id}`,
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      content: JSON.stringify(blogData)
    });

    return response.content.toJSON() as ApiResponse<Blog>;
  }

  /**
   * Delete a blog post
   */
  async deleteBlog(id: number): Promise<ApiResponse> {
    const response = await Http.request({
      url: `${this.baseUrl}/${id}`,
      method: 'DELETE'
    });

    return response.content.toJSON() as ApiResponse;
  }

  /**
   * Get blogs by topic
   */
  async getBlogsByTopic(topic: string, page: number = 1, limit: number = 10): Promise<PaginatedResponse<Blog>> {
    const url = `${this.baseUrl}?topic=${encodeURIComponent(topic)}&page=${page}&limit=${limit}`;
    const response = await Http.getJSON<PaginatedResponse<Blog>>(url);
    return response;
  }

  /**
   * Get blogs by language
   */
  async getBlogsByLanguage(language: Language, page: number = 1, limit: number = 10): Promise<PaginatedResponse<Blog>> {
    const url = `${this.baseUrl}?language=${language}&page=${page}&limit=${limit}`;
    const response = await Http.getJSON<PaginatedResponse<Blog>>(url);
    return response;
  }

  /**
   * Search blogs
   */
  async searchBlogs(query: string, page: number = 1, limit: number = 10): Promise<PaginatedResponse<Blog>> {
    const url = `${this.baseUrl}?search=${encodeURIComponent(query)}&page=${page}&limit=${limit}`;
    const response = await Http.getJSON<PaginatedResponse<Blog>>(url);
    return response;
  }

  // BLOG COMMENTS

  /**
   * Get comments for a blog post
   */
  async getBlogComments(blogId: number, page: number = 1, limit: number = 20): Promise<PaginatedResponse<BlogComment>> {
    const url = `${this.baseUrl}/${blogId}/comments?page=${page}&limit=${limit}`;
    const response = await Http.getJSON<PaginatedResponse<BlogComment>>(url);
    return response;
  }

  /**
   * Create a comment on a blog post
   */
  async createBlogComment(blogId: number, commentData: Omit<CreateBlogCommentRequest, 'blog_id'>): Promise<ApiResponse<BlogComment>> {
    const payload: CreateBlogCommentRequest = {
      ...commentData,
      blog_id: blogId
    };

    const response = await Http.request({
      url: `${this.baseUrl}/${blogId}/comments`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      content: JSON.stringify(payload)
    });

    return response.content.toJSON() as ApiResponse<BlogComment>;
  }

  /**
   * Delete a blog comment
   */
  async deleteBlogComment(blogId: number, commentId: number): Promise<ApiResponse> {
    const response = await Http.request({
      url: `${this.baseUrl}/${blogId}/comments/${commentId}`,
      method: 'DELETE'
    });

    return response.content.toJSON() as ApiResponse;
  }

  /**
   * Reply to a blog comment
   */
  async replyToBlogComment(
    blogId: number, 
    parentCommentId: number, 
    receiverId: number, 
    content: string
  ): Promise<ApiResponse<BlogComment>> {
    return this.createBlogComment(blogId, {
      parent_comment_id: parentCommentId,
      receiver_id: receiverId,
      message_type: MessageType.REPLY,
      content
    });
  }

  /**
   * Like a blog comment
   */
  async likeBlogComment(blogId: number, receiverId: number, content: string = ''): Promise<ApiResponse<BlogComment>> {
    return this.createBlogComment(blogId, {
      receiver_id: receiverId,
      message_type: MessageType.LIKE,
      content
    });
  }

  /**
   * Dislike a blog comment
   */
  async dislikeBlogComment(blogId: number, receiverId: number, content: string = ''): Promise<ApiResponse<BlogComment>> {
    return this.createBlogComment(blogId, {
      receiver_id: receiverId,
      message_type: MessageType.DISLIKE,
      content
    });
  }

  // BLOG COMMENT REACTIONS

  /**
   * Get reactions for a blog comment
   */
  async getBlogCommentReactions(blogId: number, commentId: number): Promise<ApiResponse<BlogCommentReaction[]>> {
    const url = `${this.baseUrl}/${blogId}/comments/${commentId}/reactions`;
    const response = await Http.getJSON<ApiResponse<BlogCommentReaction[]>>(url);
    return response;
  }

  /**
   * Create a reaction to a blog comment
   */
  async createBlogCommentReaction(
    blogId: number, 
    commentId: number, 
    reactionData: Omit<CreateBlogCommentReactionRequest, 'blog_id' | 'commented_on'>
  ): Promise<ApiResponse<BlogCommentReaction>> {
    const payload: CreateBlogCommentReactionRequest = {
      ...reactionData,
      blog_id: blogId,
      commented_on: commentId
    };

    const response = await Http.request({
      url: `${this.baseUrl}/${blogId}/comments/${commentId}/reactions`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      content: JSON.stringify(payload)
    });

    return response.content.toJSON() as ApiResponse<BlogCommentReaction>;
  }

  /**
   * Like a blog comment reaction
   */
  async likeBlogCommentReaction(
    blogId: number, 
    commentId: number, 
    receiverId: number, 
    content: string = ''
  ): Promise<ApiResponse<BlogCommentReaction>> {
    return this.createBlogCommentReaction(blogId, commentId, {
      receiver_id: receiverId,
      message_type: MessageType.LIKE,
      content
    });
  }

  /**
   * Dislike a blog comment reaction
   */
  async dislikeBlogCommentReaction(
    blogId: number, 
    commentId: number, 
    receiverId: number, 
    content: string = ''
  ): Promise<ApiResponse<BlogCommentReaction>> {
    return this.createBlogCommentReaction(blogId, commentId, {
      receiver_id: receiverId,
      message_type: MessageType.DISLIKE,
      content
    });
  }

  /**
   * Reply to a blog comment reaction
   */
  async replyToBlogCommentReaction(
    blogId: number, 
    commentId: number, 
    receiverId: number, 
    content: string
  ): Promise<ApiResponse<BlogCommentReaction>> {
    return this.createBlogCommentReaction(blogId, commentId, {
      receiver_id: receiverId,
      message_type: MessageType.REPLY,
      content
    });
  }

  // UTILITY METHODS

  /**
   * Get trending blogs
   */
  async getTrendingBlogs(page: number = 1, limit: number = 10): Promise<PaginatedResponse<Blog>> {
    const url = `${this.baseUrl}/trending?page=${page}&limit=${limit}`;
    const response = await Http.getJSON<PaginatedResponse<Blog>>(url);
    return response;
  }

  /**
   * Get recent blogs
   */
  async getRecentBlogs(page: number = 1, limit: number = 10): Promise<PaginatedResponse<Blog>> {
    const url = `${this.baseUrl}/recent?page=${page}&limit=${limit}`;
    const response = await Http.getJSON<PaginatedResponse<Blog>>(url);
    return response;
  }

  /**
   * Get blog topics
   */
  async getBlogTopics(): Promise<ApiResponse<string[]>> {
    const url = `${this.baseUrl}/topics`;
    const response = await Http.getJSON<ApiResponse<string[]>>(url);
    return response;
  }

  /**
   * Get blogs with most comments
   */
  async getMostCommentedBlogs(page: number = 1, limit: number = 10): Promise<PaginatedResponse<Blog>> {
    const url = `${this.baseUrl}/most-commented?page=${page}&limit=${limit}`;
    const response = await Http.getJSON<PaginatedResponse<Blog>>(url);
    return response;
  }
}
