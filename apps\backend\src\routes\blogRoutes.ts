import { Router } from 'express';
import { blogController } from '../controllers/blogController';

const router = Router();

// Blog CRUD operations
router.get('/', blogController.getAllBlogs);
router.get('/:id', blogController.getBlogById);
router.post('/', blogController.createBlog);
router.put('/:id', blogController.updateBlog);
router.delete('/:id', blogController.deleteBlog);

// Blog comments
router.get('/:blogId/comments', blogController.getBlogComments);
router.post('/:blogId/comments', blogController.createBlogComment);
router.delete('/:blogId/comments/:commentId', blogController.deleteBlogComment);

// Blog comment reactions
router.get('/:blogId/comments/:commentId/reactions', blogController.getBlogCommentReactions);
router.post('/:blogId/comments/:commentId/reactions', blogController.createBlogCommentReaction);

export default router;
