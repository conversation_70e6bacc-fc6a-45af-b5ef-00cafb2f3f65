import 'reflect-metadata';
import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { MessageType } from '../../types';
import { User } from './User.entity';
import { Group } from './Group.entity';

@Entity('group_messages')
export class GroupMessage {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  group_id: number;

  @Column()
  sender_id: number;

  @Column({ nullable: true })
  reacted_message_id?: number;

  @Column({
    type: 'enum',
    enum: MessageType
  })
  message_type: MessageType;

  @Column({ type: 'text' })
  content: string;

  @CreateDateColumn({ type: 'timestamp with time zone' })
  created_at: Date;

  // Relations
  @ManyToOne(() => Group, group => group.messages, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'group_id' })
  group: Group;

  @ManyToOne(() => User, user => user.groupMessages, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'sender_id' })
  sender: User;

  @ManyToOne(() => GroupMessage, { onDelete: 'SET NULL', nullable: true })
  @JoinColumn({ name: 'reacted_message_id' })
  reactedMessage?: GroupMessage;
}
