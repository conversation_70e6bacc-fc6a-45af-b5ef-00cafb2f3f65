import 'reflect-metadata';
import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { MessageType } from '../../types';
import { User } from './User.entity';

@Entity('private_messages')
export class PrivateMessage {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  sender_id: number;

  @Column()
  receiver_id: number;

  @Column({
    type: 'enum',
    enum: MessageType
  })
  message_type: MessageType;

  @Column({ type: 'text' })
  content: string;

  @CreateDateColumn({ type: 'timestamp with time zone' })
  created_at: Date;

  @Column({ default: false })
  is_read: boolean;

  @Column({ type: 'timestamp with time zone', nullable: true })
  read_at?: Date;

  // Relations
  @ManyToOne(() => User, user => user.sentPrivateMessages, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'sender_id' })
  sender: User;

  @ManyToOne(() => User, user => user.receivedPrivateMessages, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'receiver_id' })
  receiver: User;
}
