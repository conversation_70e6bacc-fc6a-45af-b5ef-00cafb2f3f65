import 'reflect-metadata';
import { <PERSON>tity, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { User } from './User.entity';

@Entity('reports')
export class Report {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  reporter_id: number;

  @Column()
  reported_user_id: number;

  @Column({ nullable: true })
  message_id?: number;

  @Column({ length: 20, nullable: true })
  message_type?: string; // 'group' or 'private' to identify message table

  @Column({ type: 'text' })
  reason: string;

  @CreateDateColumn({ type: 'timestamp with time zone' })
  created_at: Date;

  @Column({ length: 20, default: 'pending' })
  status: string; // pending, resolved, dismissed

  @Column({ type: 'timestamp with time zone', nullable: true })
  resolved_at?: Date;

  @Column({ nullable: true })
  resolved_by?: number;

  @Column({ type: 'text', nullable: true })
  resolution_notes?: string;

  // Relations
  @ManyToOne(() => User, user => user.reportsMade, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'reporter_id' })
  reporter: User;

  @ManyToOne(() => User, user => user.reportsReceived, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'reported_user_id' })
  reportedUser: User;

  @ManyToOne(() => User, { onDelete: 'SET NULL', nullable: true })
  @JoinColumn({ name: 'resolved_by' })
  resolver?: User;
}
