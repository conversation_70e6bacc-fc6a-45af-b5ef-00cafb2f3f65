import { Router } from 'express';
import { groupController } from '../controllers/groupController';
import { authenticateToken, optionalAuth } from '../middlewares/auth.middleware';

const router = Router();

// Public/Optional auth routes
router.get('/', optionalAuth, groupController.getAllGroups);
router.get('/:id', optionalAuth, groupController.getGroupById);

// Protected routes
router.post('/', authenticateToken, groupController.createGroup);
router.put('/:id', authenticateToken, groupController.updateGroup);
router.delete('/:id', authenticateToken, groupController.deleteGroup);

// Group member management (protected)
router.get('/:id/members', authenticateToken, groupController.getGroupMembers);
router.post('/:id/members', authenticateToken, groupController.addGroupMember);
router.delete('/:id/members/:memberId', authenticateToken, groupController.removeGroupMember);
router.put('/:id/members/:memberId/moderator', authenticateToken, groupController.updateMemberModeratorStatus);

export default router;
