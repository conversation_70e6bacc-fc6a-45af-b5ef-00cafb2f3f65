import { Http } from '@nativescript/core';
import {
  Group,
  GroupMember,
  CreateGroupRequest,
  UpdateGroupRequest,
  ApiResponse,
  PaginatedResponse,
  Language
} from '@canmoms/shared';

export class GroupService {
  private readonly baseUrl = 'http://localhost:3000/api/groups';

  /**
   * Get all groups with pagination and filters
   */
  async getAllGroups(
    page: number = 1, 
    limit: number = 10,
    filters?: {
      country?: string;
      city?: string;
      language?: Language;
      topic?: string;
      isPublic?: boolean;
    }
  ): Promise<PaginatedResponse<Group>> {
    let url = `${this.baseUrl}?page=${page}&limit=${limit}`;

    if (filters) {
      if (filters.country) url += `&country=${encodeURIComponent(filters.country)}`;
      if (filters.city) url += `&city=${encodeURIComponent(filters.city)}`;
      if (filters.language) url += `&language=${filters.language}`;
      if (filters.topic) url += `&topic=${encodeURIComponent(filters.topic)}`;
      if (filters.isPublic !== undefined) url += `&isPublic=${filters.isPublic}`;
    }

    const response = await Http.getJSON<PaginatedResponse<Group>>(url);
    return response;
  }

  /**
   * Get group by ID
   */
  async getGroupById(id: number): Promise<ApiResponse<Group>> {
    const url = `${this.baseUrl}/${id}`;
    const response = await Http.getJSON<ApiResponse<Group>>(url);
    return response;
  }

  /**
   * Create a new group
   */
  async createGroup(groupData: CreateGroupRequest): Promise<ApiResponse<Group>> {
    const response = await Http.request({
      url: this.baseUrl,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      content: JSON.stringify(groupData)
    });

    return response.content.toJSON() as ApiResponse<Group>;
  }

  /**
   * Update an existing group
   */
  async updateGroup(id: number, groupData: UpdateGroupRequest): Promise<ApiResponse<Group>> {
    const response = await Http.request({
      url: `${this.baseUrl}/${id}`,
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      content: JSON.stringify(groupData)
    });

    return response.content.toJSON() as ApiResponse<Group>;
  }

  /**
   * Delete a group
   */
  async deleteGroup(id: number): Promise<ApiResponse> {
    const response = await Http.request({
      url: `${this.baseUrl}/${id}`,
      method: 'DELETE'
    });

    return response.content.toJSON() as ApiResponse;
  }

  /**
   * Get group members
   */
  async getGroupMembers(groupId: number): Promise<ApiResponse<GroupMember[]>> {
    const url = `${this.baseUrl}/${groupId}/members`;
    const response = await Http.getJSON<ApiResponse<GroupMember[]>>(url);
    return response;
  }

  /**
   * Add member to group
   */
  async addGroupMember(groupId: number, userId: number, isModerator: boolean = false): Promise<ApiResponse<GroupMember>> {
    const response = await Http.request({
      url: `${this.baseUrl}/${groupId}/members`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      content: JSON.stringify({
        user_id: userId,
        is_moderator: isModerator
      })
    });

    return response.content.toJSON() as ApiResponse<GroupMember>;
  }

  /**
   * Remove member from group
   */
  async removeGroupMember(groupId: number, memberId: number): Promise<ApiResponse> {
    const response = await Http.request({
      url: `${this.baseUrl}/${groupId}/members/${memberId}`,
      method: 'DELETE'
    });

    return response.content.toJSON() as ApiResponse;
  }

  /**
   * Update member moderator status
   */
  async updateMemberModeratorStatus(groupId: number, memberId: number, isModerator: boolean): Promise<ApiResponse<GroupMember>> {
    const response = await Http.request({
      url: `${this.baseUrl}/${groupId}/members/${memberId}/moderator`,
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      content: JSON.stringify({
        is_moderator: isModerator
      })
    });

    return response.content.toJSON() as ApiResponse<GroupMember>;
  }

  /**
   * Join a group (current user)
   */
  async joinGroup(groupId: number): Promise<ApiResponse<GroupMember>> {
    const response = await Http.request({
      url: `${this.baseUrl}/${groupId}/join`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      content: JSON.stringify({})
    });

    return response.content.toJSON() as ApiResponse<GroupMember>;
  }

  /**
   * Leave a group (current user)
   */
  async leaveGroup(groupId: number): Promise<ApiResponse> {
    const response = await Http.request({
      url: `${this.baseUrl}/${groupId}/leave`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      content: JSON.stringify({})
    });

    return response.content.toJSON() as ApiResponse;
  }

  /**
   * Get groups by location
   */
  async getGroupsByLocation(country: string, city?: string, page: number = 1, limit: number = 10): Promise<PaginatedResponse<Group>> {
    let url = `${this.baseUrl}/location?country=${encodeURIComponent(country)}&page=${page}&limit=${limit}`;
    
    if (city) {
      url += `&city=${encodeURIComponent(city)}`;
    }

    const response = await Http.getJSON<PaginatedResponse<Group>>(url);
    return response;
  }

  /**
   * Get groups by topic
   */
  async getGroupsByTopic(topic: string, page: number = 1, limit: number = 10): Promise<PaginatedResponse<Group>> {
    const url = `${this.baseUrl}/topic?topic=${encodeURIComponent(topic)}&page=${page}&limit=${limit}`;
    const response = await Http.getJSON<PaginatedResponse<Group>>(url);
    return response;
  }

  /**
   * Get groups by language
   */
  async getGroupsByLanguage(language: Language, page: number = 1, limit: number = 10): Promise<PaginatedResponse<Group>> {
    const url = `${this.baseUrl}/language?language=${language}&page=${page}&limit=${limit}`;
    const response = await Http.getJSON<PaginatedResponse<Group>>(url);
    return response;
  }

  /**
   * Search groups
   */
  async searchGroups(query: string, page: number = 1, limit: number = 10): Promise<PaginatedResponse<Group>> {
    const url = `${this.baseUrl}?search=${encodeURIComponent(query)}&page=${page}&limit=${limit}`;
    const response = await Http.getJSON<PaginatedResponse<Group>>(url);
    return response;
  }

  /**
   * Get user's groups
   */
  async getUserGroups(userId?: number, page: number = 1, limit: number = 10): Promise<PaginatedResponse<Group>> {
    let url = `${this.baseUrl}/user`;
    if (userId) {
      url += `/${userId}`;
    }
    url += `?page=${page}&limit=${limit}`;

    const response = await Http.getJSON<PaginatedResponse<Group>>(url);
    return response;
  }

  /**
   * Get recommended groups for user
   */
  async getRecommendedGroups(page: number = 1, limit: number = 10): Promise<PaginatedResponse<Group>> {
    const url = `${this.baseUrl}/recommended?page=${page}&limit=${limit}`;
    const response = await Http.getJSON<PaginatedResponse<Group>>(url);
    return response;
  }

  /**
   * Get public groups
   */
  async getPublicGroups(page: number = 1, limit: number = 10): Promise<PaginatedResponse<Group>> {
    const url = `${this.baseUrl}?isPublic=true&page=${page}&limit=${limit}`;
    const response = await Http.getJSON<PaginatedResponse<Group>>(url);
    return response;
  }

  /**
   * Get groups by birth year
   */
  async getGroupsByBirthYear(birthYear: number, page: number = 1, limit: number = 10): Promise<PaginatedResponse<Group>> {
    const url = `${this.baseUrl}?birth_year=${birthYear}&page=${page}&limit=${limit}`;
    const response = await Http.getJSON<PaginatedResponse<Group>>(url);
    return response;
  }
}
