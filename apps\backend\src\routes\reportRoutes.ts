import { Router } from 'express';
import { reportController } from '../controllers/reportController';

const router = Router();

// Report CRUD operations
router.get('/', reportController.getAllReports);
router.get('/:id', reportController.getReportById);
router.post('/', reportController.createReport);
router.delete('/:id', reportController.deleteReport);

// Report analytics
router.get('/user/:userId', reportController.getReportsByUser);
router.get('/stats', reportController.getReportStats);

export default router;
