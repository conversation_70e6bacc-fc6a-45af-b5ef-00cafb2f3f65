import { Router } from 'express';
import { userController } from '../controllers/userController';
import { authenticateToken, requireAdmin, optionalAuth } from '../middlewares/auth.middleware';

const router = Router();

// Public routes
router.get('/', optionalAuth, userController.getAllUsers);
router.get('/:id', optionalAuth, userController.getUserById);

// Protected routes
router.post('/', authenticateToken, userController.createUser);
router.put('/:id', authenticateToken, userController.updateUser);
router.delete('/:id', authenticateToken, userController.deleteUser);

// Admin only routes
router.post('/:id/ban', authenticateToken, requireAdmin, userController.banUser);
router.post('/:id/unban', authenticateToken, requireAdmin, userController.unbanUser);

export default router;
